"""知识库方法测试

测试 create_knowledgebase、search_knowledgebase、list_knowledgebases 方法
直接调用真实接口，不使用 mock

# 运行所有知识库测试
pytest tests/test_knowledgebase.py -v

# 运行特定测试方法
pytest tests/test_knowledgebase.py::TestKnowledgeBase::test_create_knowledgebase_success -v

# 运行测试并显示详细输出
pytest tests/test_knowledgebase.py -v -s
"""
import json
from pprint import pprint

import pytest

from app.client.volcengine_knowledgebase import get_knowledgebase_client, search_knowledgebase
from app.schemas.knowledgebase import CollectionInfo

client = get_knowledgebase_client()


class TestKnowledgeBase:
    """知识库测试类"""

    def test_config_loaded(self, validate_config):
        """测试配置是否正确加载"""
        assert validate_config.VOLCENGINE_ACCESS_KEY is not None
        assert validate_config.VOLCENGINE_SECRET_KEY is not None
        assert validate_config.VOLCENGINE_KB_HOST is not None
        print(f"配置加载成功: {validate_config.VOLCENGINE_KB_HOST}")

    # def test_create_knowledgebase_with_config(self, validate_config):
    #     """测试带配置参数创建知识库"""
    #     # 测试数据
    #     collection_name = "test_collection_002"
    #     description = "测试知识库配置"
    #     config = {"vector_size": 1024, "similarity_metric": "cosine"}
    #
    #     # 调用方法
    #     result = create_knowledgebase(collection_name=collection_name, description=description, config=config)
    #
    #     # 断言结果
    #     assert isinstance(result, OperationResponse)
    #     assert result.success is True
    #     assert result.message is not None
    #     assert result.data is not None
    #     assert result.data["collection_name"] == collection_name
    #

    def test_list_knowledgebases_success(self, validate_config):
        """测试成功列出知识库"""
        # 调用方法
        result = client.list_collections()
        pprint(result)


        # 断言结果
        assert isinstance(result, list)

    def test_list_docs(self):
        docs = client.list_docs()
        for doc in docs:
            print(f"collection_name: {doc.collection_name}, doc_name: {doc.doc_name}, doc_id: {doc.doc_id}, doc_type: {doc.doc_type}, create_time: {doc.create_time}, added_by: {doc.added_by}, update_time: {doc.update_time}, url: {doc.url}, tos_path: {doc.tos_path}, point_num: {doc.point_num}, status: {doc.status}, title: {doc.title}, source: {doc.source}, project: {doc.project}, resource_id: {doc.resource_id}, fields: {[field.__dict__ if hasattr(field, '__dict__') else str(field) for field in doc.fields]}")
        assert isinstance(docs, list)

    def test_search_knowledgebase_with_params(self, validate_config):
        """测试带参数搜索知识库"""
        # 使用第一个知识库进行搜索
        collection_name = client.get_default_collection().collection_name
        query = "医学"
        limit = 5
        dense_weight = 0.8
        rerank_switch = False

        # 调用方法
        result = search_knowledgebase(
            collection_name=collection_name,
            query=query,
            limit=limit,
            dense_weight=dense_weight,
            rerank_switch=rerank_switch,
        )

        # 断言结果
        assert isinstance(result, list)
        assert len(result) <= limit

        # 检查每个 KnowledgePoint 对象的属性
        for point in result:
            pprint(point.rerank_score, point.content)

    def test_search_knowledgebase_knowledge_point_structure(self, validate_config):
        """测试搜索结果中 KnowledgePoint 的结构"""
        collection_name = client.get_default_collection().collection_name
        query = "健康"
        # 调用方法
        result = search_knowledgebase(collection_name=collection_name, query=query, limit=3)
        pprint(result)

        # 断言结果
        assert isinstance(result, list)

        # 检查每个 KnowledgePoint 的详细结构
        for point in result:
            pprint(point.score)
            pprint(point.rerank_score)
            pprint(point.content)
            pprint(point.doc_info)
            pprint("-----------------------------------")
