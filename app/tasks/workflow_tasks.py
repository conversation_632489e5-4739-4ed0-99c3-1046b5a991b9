"""
工作流任务模块
"""

import logging
from typing import Dict, List
from datetime import datetime

from celery import chain, group, chord
from app.core.celery_config import celery_app
from app.tasks.base_task import BaseWorkflowTask, RetryableError, FatalError

# 导入具体任务
from app.tasks.ocr_tasks import ocr_task
from app.tasks.ai_tasks import (
    meta_analysis_task, tabular_analysis_task, 
    overall_analysis_task, image_analysis_task, 
    diseases_extraction_task
)
from app.tasks.post_process_tasks import translation_task, indicator_conversion_task
from app.tasks.storage_tasks import call_api_task, store_vector_task

logger = logging.getLogger(__name__)


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def create_report_workflow(self, report_id: str, report_key: str):
    """
    创建并执行报告处理工作流
    
    Args:
        report_id: 报告ID
        report_key: 报告密钥
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        if not report_key:
            raise FatalError("Missing required parameter: report_key")
        
        logger.info(f"Creating workflow for report {report_id}")
        
        # 更新主任务状态为处理中
        _update_main_task_status(report_id, 'processing')
        
        # 步骤1: OCR处理
        ocr_step = ocr_task.s(report_id=report_id, report_key=report_key)
        
        # 步骤2: AI分析并行任务组
        ai_analysis_group = group(
            meta_analysis_task.s(report_id=report_id),
            tabular_analysis_task.s(report_id=report_id),
            overall_analysis_task.s(report_id=report_id),
            image_analysis_task.s(report_id=report_id),
            diseases_extraction_task.s(report_id=report_id)
        )
        
        # 步骤3: 后处理串行任务
        post_processing_chain = chain(
            translation_task.s(report_id=report_id),
            indicator_conversion_task.s(report_id=report_id)
        )
        
        # 步骤4: 存储并行任务
        storage_group = group(
            call_api_task.s(report_id=report_id),
            store_vector_task.s(report_id=report_id)
        )
        
        # 组合完整工作流
        workflow = chain(
            ocr_step,
            chord(ai_analysis_group, post_processing_chain),
            storage_group,
            finalize_workflow.s(report_id=report_id)
        )
        
        # 执行工作流
        result = workflow.apply_async()
        
        # 保存工作流ID
        _save_workflow_id(report_id, result.id)
        
        logger.info(f"Workflow created and started for report {report_id}, workflow_id: {result.id}")
        
        return {
            'success': True,
            'report_id': report_id,
            'workflow_id': result.id,
            'status': 'started'
        }
        
    except Exception as e:
        logger.error(f"Failed to create workflow for report {report_id}: {e}")
        _update_main_task_status(report_id, 'failed', str(e))
        if isinstance(e, (FatalError, RetryableError)):
            raise
        else:
            raise FatalError(f"Workflow creation error: {e}")


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def finalize_workflow(self, results: List[Dict], report_id: str):
    """
    完成工作流处理
    
    Args:
        results: 前面步骤的结果列表
        report_id: 报告ID
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        logger.info(f"Finalizing workflow for report {report_id}")
        
        # 汇总所有结果
        summary = _summarize_results(results, report_id)
        
        # 更新主任务状态为完成
        _update_main_task_status(report_id, 'completed')
        
        # 保存最终汇总结果
        self._save_task_result(report_id, 'final_summary', summary)
        
        logger.info(f"Workflow completed for report {report_id}")
        
        return {
            'success': True,
            'report_id': report_id,
            'status': 'completed',
            'summary': summary
        }
        
    except Exception as e:
        logger.error(f"Failed to finalize workflow for report {report_id}: {e}")
        _update_main_task_status(report_id, 'failed', str(e))
        if isinstance(e, (FatalError, RetryableError)):
            raise
        else:
            raise FatalError(f"Workflow finalization error: {e}")


def _update_main_task_status(report_id: str, status: str, error_message: str = None):
        """更新主任务状态"""
        try:
            from app.models import get_db, ReportTask
            
            db = next(get_db())
            try:
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                if task:
                    task.status = status
                    task.updated_at = datetime.utcnow()
                    
                    if status == 'completed':
                        task.completed_at = datetime.utcnow()
                    elif error_message:
                        task.error_message = error_message
                    
                    db.commit()
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to update main task status: {e}")
    
def _save_workflow_id(report_id: str, workflow_id: str):
        """保存工作流ID"""
        try:
            from app.models import get_db, ReportTask
            
            db = next(get_db())
            try:
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                if task:
                    task.workflow_id = workflow_id
                    task.updated_at = datetime.utcnow()
                    db.commit()
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to save workflow ID: {e}")
    
@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def cancel_workflow(self, report_id: str, reason: str = "User requested"):
    """
    取消工作流执行
    
    Args:
        report_id: 报告ID
        reason: 取消原因
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        logger.info(f"Cancelling workflow for report {report_id}, reason: {reason}")
        
        # 获取工作流ID
        workflow_id = _get_workflow_id(report_id)
        if workflow_id:
            # 撤销Celery任务
            celery_app.control.revoke(workflow_id, terminate=True)
            logger.info(f"Revoked Celery task {workflow_id}")
        
        # 更新任务状态
        _update_main_task_status(report_id, 'cancelled', f"Cancelled: {reason}")
        
        # 执行清理操作
        _cleanup_workflow_resources(report_id)
        
        return {
            'success': True,
            'report_id': report_id,
            'status': 'cancelled',
            'reason': reason
        }
        
    except Exception as e:
        logger.error(f"Failed to cancel workflow for report {report_id}: {e}")
        raise FatalError(f"Workflow cancellation error: {e}")


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def restart_workflow_from_step(self, report_id: str, from_step: str, cleanup_partial: bool = True):
    """
    从指定步骤重启工作流
    
    Args:
        report_id: 报告ID
        from_step: 重启的步骤名称
        cleanup_partial: 是否清理部分结果
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        logger.info(f"Restarting workflow for report {report_id} from step {from_step}")
        
        # 取消现有工作流
        cancel_workflow.delay(report_id, f"Restarting from {from_step}")
        
        # 清理部分结果（如果需要）
        if cleanup_partial:
            _cleanup_partial_results(report_id, from_step)
        
        # 获取报告密钥
        report_key = _get_report_key(report_id)
        if not report_key:
            raise FatalError(f"Cannot find report_key for {report_id}")
        
        # 根据步骤创建新的工作流
        workflow = _create_workflow_from_step(report_id, report_key, from_step)
        
        # 执行工作流
        result = workflow.apply_async()
        
        # 保存新的工作流ID
        _save_workflow_id(report_id, result.id)
        
        logger.info(f"Workflow restarted for report {report_id}, new workflow_id: {result.id}")
        
        return {
            'success': True,
            'report_id': report_id,
            'workflow_id': result.id,
            'restarted_from': from_step,
            'status': 'restarted'
        }
        
    except Exception as e:
        logger.error(f"Failed to restart workflow for report {report_id}: {e}")
        raise FatalError(f"Workflow restart error: {e}")


def _get_workflow_id(report_id: str) -> str:
    """获取工作流ID"""
    try:
        from app.models import get_db, ReportTask
        
        db = next(get_db())
        try:
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
            return task.workflow_id if task else None
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Failed to get workflow ID: {e}")
        return None


def _get_report_key(report_id: str) -> str:
    """获取报告密钥"""
    try:
        from app.models import get_db, ReportTask
        
        db = next(get_db())
        try:
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
            return task.report_key if task else None
        finally:
            db.close()
    except Exception as e:
        logger.error(f"Failed to get report key: {e}")
        return None


def _create_workflow_from_step(report_id: str, report_key: str, from_step: str):
    """从指定步骤创建工作流"""
    
    # 定义步骤映射
    step_workflows = {
        'ocr': lambda: chain(
            ocr_task.s(report_id=report_id, report_key=report_key),
            chord(
                group(
                    meta_analysis_task.s(report_id=report_id),
                    tabular_analysis_task.s(report_id=report_id),
                    overall_analysis_task.s(report_id=report_id),
                    image_analysis_task.s(report_id=report_id),
                    diseases_extraction_task.s(report_id=report_id)
                ),
                chain(
                    translation_task.s(report_id=report_id),
                    indicator_conversion_task.s(report_id=report_id)
                )
            ),
            group(
                call_api_task.s(report_id=report_id),
                store_vector_task.s(report_id=report_id)
            ),
            finalize_workflow.s(report_id=report_id)
        ),
        'ai_analysis': lambda: chain(
            chord(
                group(
                    meta_analysis_task.s(report_id=report_id),
                    tabular_analysis_task.s(report_id=report_id),
                    overall_analysis_task.s(report_id=report_id),
                    image_analysis_task.s(report_id=report_id),
                    diseases_extraction_task.s(report_id=report_id)
                ),
                chain(
                    translation_task.s(report_id=report_id),
                    indicator_conversion_task.s(report_id=report_id)
                )
            ),
            group(
                call_api_task.s(report_id=report_id),
                store_vector_task.s(report_id=report_id)
            ),
            finalize_workflow.s(report_id=report_id)
        ),
        'post_processing': lambda: chain(
            chain(
                translation_task.s(report_id=report_id),
                indicator_conversion_task.s(report_id=report_id)
            ),
            group(
                call_api_task.s(report_id=report_id),
                store_vector_task.s(report_id=report_id)
            ),
            finalize_workflow.s(report_id=report_id)
        ),
        'storage': lambda: chain(
            group(
                call_api_task.s(report_id=report_id),
                store_vector_task.s(report_id=report_id)
            ),
            finalize_workflow.s(report_id=report_id)
        )
    }
    
    workflow_factory = step_workflows.get(from_step)
    if not workflow_factory:
        raise FatalError(f"Unknown step: {from_step}")
    
    return workflow_factory()


def _cleanup_workflow_resources(report_id: str):
    """清理工作流资源"""
    try:
        logger.info(f"Cleaning up workflow resources for report {report_id}")
        
        # 清理临时文件
        _cleanup_temp_files(report_id)
        
        # 清理缓存
        _cleanup_cache_entries(report_id)
        
        # 记录清理操作
        from app.models import get_db, TaskStep
        db = next(get_db())
        try:
            cleanup_step = TaskStep(
                report_id=report_id,
                step_name='workflow_cleanup',
                status='completed',
                start_time=datetime.utcnow(),
                end_time=datetime.utcnow(),
                duration=0
            )
            db.add(cleanup_step)
            db.commit()
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to cleanup workflow resources: {e}")


def _cleanup_partial_results(report_id: str, from_step: str):
    """清理部分结果"""
    try:
        logger.info(f"Cleaning up partial results for report {report_id} from step {from_step}")
        
        from app.models import get_db, TaskResult, TaskStep
        
        # 定义需要清理的步骤
        cleanup_steps = {
            'ocr': ['ocr', 'meta', 'tabular', 'overall', 'image', 'diseases', 'translation', 'indicator_conversion', 'api_call', 'vector_storage'],
            'ai_analysis': ['meta', 'tabular', 'overall', 'image', 'diseases', 'translation', 'indicator_conversion', 'api_call', 'vector_storage'],
            'post_processing': ['translation', 'indicator_conversion', 'api_call', 'vector_storage'],
            'storage': ['api_call', 'vector_storage']
        }
        
        steps_to_clean = cleanup_steps.get(from_step, [])
        
        db = next(get_db())
        try:
            # 删除相关的任务结果
            db.query(TaskResult).filter(
                TaskResult.report_id == report_id,
                TaskResult.result_type.in_(steps_to_clean)
            ).delete(synchronize_session=False)
            
            # 重置相关的任务步骤
            db.query(TaskStep).filter(
                TaskStep.report_id == report_id,
                TaskStep.step_name.contains(from_step)
            ).delete(synchronize_session=False)
            
            db.commit()
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to cleanup partial results: {e}")


def _cleanup_temp_files(report_id: str):
    """清理临时文件"""
    try:
        import os
        import glob
        
        # 清理临时目录中的相关文件
        temp_patterns = [
            f"/tmp/*{report_id}*",
            f"/data/temp/*{report_id}*",
            f"/data/reports/temp/*{report_id}*"
        ]
        
        for pattern in temp_patterns:
            for file_path in glob.glob(pattern):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        logger.debug(f"Removed temp file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove temp file {file_path}: {e}")
                    
    except Exception as e:
        logger.error(f"Failed to cleanup temp files: {e}")


def _cleanup_cache_entries(report_id: str):
    """清理缓存条目"""
    try:
        # 清理OCR缓存
        from app.services.report_task_processor import get_task_processor
        processor = get_task_processor()
        if hasattr(processor, '_ocr_cache') and report_id in processor._ocr_cache:
            del processor._ocr_cache[report_id]
            logger.debug(f"Removed OCR cache for report {report_id}")
            
    except Exception as e:
        logger.error(f"Failed to cleanup cache entries: {e}")


def _calculate_workflow_progress(report_id: str) -> float:
    """计算工作流进度"""
    try:
        from app.models import get_db, TaskStep
        
        db = next(get_db())
        try:
            steps = db.query(TaskStep).filter(TaskStep.report_id == report_id).all()
            
            if not steps:
                return 0.0
            
            # 定义步骤权重
            step_weights = {
                'ocr_task': 20.0,
                'meta_analysis_task': 15.0,
                'tabular_analysis_task': 15.0,
                'overall_analysis_task': 15.0,
                'image_analysis_task': 10.0,
                'diseases_extraction_task': 10.0,
                'translation_task': 5.0,
                'indicator_conversion_task': 5.0,
                'call_api_task': 2.5,
                'store_vector_task': 2.5
            }
            
            total_weight = sum(step_weights.values())
            completed_weight = 0.0
            
            for step in steps:
                step_name = step.step_name.split('.')[-1] if '.' in step.step_name else step.step_name
                weight = step_weights.get(step_name, 1.0)
                
                if step.status == 'completed':
                    completed_weight += weight
                elif step.status == 'processing':
                    completed_weight += weight * 0.5  # 处理中算50%
            
            return (completed_weight / total_weight) * 100.0
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to calculate workflow progress: {e}")
        return 0.0


def _summarize_results(results: List[Dict], report_id: str) -> Dict:
        """汇总工作流结果"""
        try:
            summary = {
                'report_id': report_id,
                'completed_at': datetime.utcnow().isoformat(),
                'total_steps': len(results) if results else 0,
                'successful_steps': 0,
                'failed_steps': 0,
                'step_results': [],
                'progress_percent': _calculate_workflow_progress(report_id)
            }
            
            if results:
                for result in results:
                    if isinstance(result, dict):
                        if result.get('success', False):
                            summary['successful_steps'] += 1
                        else:
                            summary['failed_steps'] += 1
                        summary['step_results'].append(result)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to summarize results: {e}")
            return {
                'report_id': report_id,
                'error': f"Failed to summarize: {e}",
                'completed_at': datetime.utcnow().isoformat()
            }