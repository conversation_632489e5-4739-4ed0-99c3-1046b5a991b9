"""
Celery基础任务类
"""

import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, Optional

from celery import Task
from celery.exceptions import MaxRetriesExceededError, Retry
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models import get_db

logger = logging.getLogger(__name__)


class WorkflowError(Exception):
    """工作流错误基类"""
    pass


class RetryableError(WorkflowError):
    """可重试的错误"""
    pass


class FatalError(WorkflowError):
    """致命错误，不应重试"""
    pass


class TimeoutError(RetryableError):
    """超时错误"""
    pass


class ExternalServiceError(RetryableError):
    """外部服务错误"""
    pass


class BaseWorkflowTask(Task):
    """工作流任务基类"""
    
    # 自动重试配置
    autoretry_for = (RetryableError, ConnectionError, TimeoutError)
    retry_kwargs = {'max_retries': 3}
    retry_backoff = True
    retry_backoff_max = 600  # 最大退避时间10分钟
    retry_jitter = True      # 添加随机抖动
    
    def before_start(self, task_id, args, kwargs):
        """任务开始前的钩子"""
        report_id = kwargs.get('report_id')
        if report_id:
            self._update_step_status(
                report_id=report_id,
                step_name=self.name,
                status='processing',
                celery_task_id=task_id,
                start_time=datetime.utcnow()
            )
            logger.info(f"Task {self.name} started for report {report_id}", extra={
                'task_id': task_id,
                'report_id': report_id,
                'task_name': self.name
            })
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功的钩子"""
        report_id = kwargs.get('report_id')
        if report_id:
            self._update_step_status(
                report_id=report_id,
                step_name=self.name,
                status='completed',
                celery_task_id=task_id,
                end_time=datetime.utcnow(),
                result=retval
            )
            logger.info(f"Task {self.name} completed for report {report_id}", extra={
                'task_id': task_id,
                'report_id': report_id,
                'task_name': self.name,
                'result_size': len(str(retval)) if retval else 0
            })
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败的钩子"""
        report_id = kwargs.get('report_id')
        if report_id:
            self._update_step_status(
                report_id=report_id,
                step_name=self.name,
                status='failed',
                celery_task_id=task_id,
                end_time=datetime.utcnow(),
                error_message=str(exc)
            )
            
            # 记录详细错误信息
            self._record_task_error(
                report_id=report_id,
                task_name=self.name,
                error=exc,
                traceback_str=einfo.traceback
            )
            
            # 检查是否需要执行补偿操作
            if self.request.retries >= self.max_retries:
                self._trigger_compensation(report_id, exc, einfo)
            
            logger.error(f"Task {self.name} failed for report {report_id}: {exc}", extra={
                'task_id': task_id,
                'report_id': report_id,
                'task_name': self.name,
                'error_type': type(exc).__name__,
                'error_message': str(exc),
                'traceback': einfo.traceback
            })
    
    def _trigger_compensation(self, report_id: str, exc: Exception, einfo):
        """触发补偿操作"""
        try:
            # 延迟导入避免循环依赖
            from app.tasks.workflow_compensation import execute_compensation_actions
            
            error_details = {
                'error': str(exc),
                'error_type': type(exc).__name__,
                'traceback': einfo.traceback,
                'retries': self.request.retries,
                'task_id': self.request.id
            }
            
            # 异步执行补偿操作
            execute_compensation_actions.delay(
                report_id=report_id,
                failed_step=self.name,
                error_details=error_details
            )
            
            logger.info(f"Triggered compensation for failed task {self.name} in report {report_id}")
            
        except Exception as e:
            logger.error(f"Failed to trigger compensation: {e}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试的钩子"""
        report_id = kwargs.get('report_id')
        retry_count = self.request.retries
        
        if report_id:
            logger.warning(
                f"Task {self.name} retry {retry_count + 1} for report {report_id}: {exc}",
                extra={
                    'task_id': task_id,
                    'report_id': report_id,
                    'task_name': self.name,
                    'retry_count': retry_count + 1,
                    'error_type': type(exc).__name__,
                    'error_message': str(exc)
                }
            )
    
    def _update_step_status(self, report_id: str, step_name: str, status: str,
                          celery_task_id: str = None, start_time: datetime = None,
                          end_time: datetime = None, result: Any = None,
                          error_message: str = None):
        """更新步骤状态到数据库"""
        try:
            db = next(get_db())
            try:
                # 导入模型（延迟导入避免循环依赖）
                from app.models import TaskStep
                
                # 查找或创建任务步骤记录
                step = db.query(TaskStep).filter(
                    TaskStep.report_id == report_id,
                    TaskStep.step_name == step_name
                ).first()
                
                if not step:
                    step = TaskStep(
                        report_id=report_id,
                        step_name=step_name,
                        status=status,
                        celery_task_id=celery_task_id
                    )
                    db.add(step)
                else:
                    step.status = status
                    if celery_task_id:
                        step.celery_task_id = celery_task_id
                
                # 更新时间戳
                if start_time:
                    step.start_time = start_time
                if end_time:
                    step.end_time = end_time
                    if step.start_time:
                        step.duration = int((end_time - step.start_time).total_seconds())
                
                # 更新结果和错误信息
                if result is not None:
                    step.result = json.dumps(result) if not isinstance(result, str) else result
                if error_message:
                    step.error_message = error_message
                
                db.commit()
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to update step status: {e}", extra={
                'report_id': report_id,
                'step_name': step_name,
                'status': status,
                'error': str(e)
            })
    
    def _save_task_result(self, report_id: str, result_type: str, 
                         result_data: Dict, file_path: str = None):
        """保存任务结果到数据库"""
        try:
            db = next(get_db())
            try:
                # 导入模型（延迟导入避免循环依赖）
                from app.models import TaskResult
                
                # 将结果数据转换为JSON字符串
                if isinstance(result_data, dict):
                    result_json = json.dumps(result_data, ensure_ascii=False)
                else:
                    result_json = str(result_data)
                
                task_result = TaskResult(
                    report_id=report_id,
                    result_type=result_type,
                    result_data=result_json,
                    file_path=file_path
                )
                
                db.add(task_result)
                db.commit()
                
                logger.info(f"Saved task result: {result_type} for report {report_id}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to save task result: {e}", extra={
                'report_id': report_id,
                'result_type': result_type,
                'error': str(e)
            })
    
    def _record_task_error(self, report_id: str, task_name: str, 
                          error: Exception, traceback_str: str = None):
        """记录任务错误到数据库"""
        try:
            db = next(get_db())
            try:
                from app.models import TaskError
                
                task_error = TaskError(
                    report_id=report_id,
                    task_name=task_name,
                    error_type=type(error).__name__,
                    error_message=str(error),
                    traceback=traceback_str,
                    celery_task_id=getattr(self.request, 'id', None),
                    retry_count=getattr(self.request, 'retries', 0)
                )
                
                db.add(task_error)
                db.commit()
                
                logger.info(f"Recorded task error for {task_name} in report {report_id}")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to record task error: {e}", extra={
                'report_id': report_id,
                'task_name': task_name,
                'original_error': str(error),
                'record_error': str(e)
            })
    
    def retry_with_backoff(self, exc=None, countdown=None, **kwargs):
        """带退避策略的重试"""
        if countdown is None:
            # 指数退避：2^retry_count * 60秒，最大600秒
            retry_count = self.request.retries
            countdown = min(60 * (2 ** retry_count), 600)
        
        raise self.retry(exc=exc, countdown=countdown, **kwargs)
    
    def handle_external_service_error(self, service_name: str, error: Exception):
        """处理外部服务错误"""
        error_msg = f"{service_name} service error: {error}"
        logger.error(error_msg, extra={
            'service_name': service_name,
            'error_type': type(error).__name__,
            'error_message': str(error)
        })
        
        # 根据错误类型决定是否重试
        if isinstance(error, (ConnectionError, TimeoutError)):
            raise RetryableError(error_msg)
        else:
            raise FatalError(error_msg)
    
    def validate_task_input(self, **kwargs):
        """验证任务输入参数"""
        report_id = kwargs.get('report_id')
        if not report_id:
            raise FatalError("Missing required parameter: report_id")
        
        return report_id