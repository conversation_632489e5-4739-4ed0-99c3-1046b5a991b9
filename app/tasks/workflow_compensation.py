"""
工作流补偿和错误处理模块
"""

import logging
from typing import Dict, List, Optional
from datetime import datetime

from celery import group
from app.core.celery_config import celery_app
from app.tasks.base_task import BaseWorkflowTask, RetryableError, FatalError

logger = logging.getLogger(__name__)


class WorkflowCompensationManager:
    """工作流补偿管理器"""
    
    @staticmethod
    def get_compensation_actions(failed_step: str, report_id: str) -> List[Dict]:
        """获取失败步骤的补偿操作"""
        
        compensation_map = {
            'ocr_task': [
                {'action': 'cleanup_temp_files', 'params': {'report_id': report_id}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'ocr'}},
                {'action': 'clear_ocr_cache', 'params': {'report_id': report_id}}
            ],
            'meta_analysis_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'meta'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'meta_analysis'}}
            ],
            'tabular_analysis_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'tabular'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'tabular_analysis'}}
            ],
            'overall_analysis_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'overall'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'overall_analysis'}}
            ],
            'image_analysis_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'image'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'image_analysis'}}
            ],
            'diseases_extraction_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'diseases'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'diseases_extraction'}}
            ],
            'translation_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'translation'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'translation'}}
            ],
            'indicator_conversion_task': [
                {'action': 'cleanup_partial_results', 'params': {'report_id': report_id, 'result_type': 'indicator_conversion'}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'indicator_conversion'}}
            ],
            'call_api_task': [
                {'action': 'cleanup_api_resources', 'params': {'report_id': report_id}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'api_call'}}
            ],
            'store_vector_task': [
                {'action': 'cleanup_vector_data', 'params': {'report_id': report_id}},
                {'action': 'reset_task_status', 'params': {'report_id': report_id, 'step': 'vector_storage'}}
            ]
        }
        
        return compensation_map.get(failed_step, [])


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def execute_compensation_actions(self, report_id: str, failed_step: str, error_details: Dict = None):
    """
    执行补偿操作
    
    Args:
        report_id: 报告ID
        failed_step: 失败的步骤名称
        error_details: 错误详情
    """
    try:
        self.validate_task_input(report_id=report_id)
        
        logger.info(f"Executing compensation actions for report {report_id}, failed step: {failed_step}")
        
        # 获取补偿操作列表
        compensation_actions = WorkflowCompensationManager.get_compensation_actions(failed_step, report_id)
        
        if not compensation_actions:
            logger.warning(f"No compensation actions defined for step: {failed_step}")
            return {'success': True, 'message': 'No compensation actions needed'}
        
        # 执行补偿操作
        results = []
        for action in compensation_actions:
            try:
                result = _execute_single_compensation(action['action'], action['params'])
                results.append({
                    'action': action['action'],
                    'success': True,
                    'result': result
                })
                logger.info(f"Compensation action {action['action']} completed successfully")
                
            except Exception as e:
                results.append({
                    'action': action['action'],
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"Compensation action {action['action']} failed: {e}")
        
        # 记录补偿操作
        _record_compensation_execution(report_id, failed_step, results, error_details)
        
        return {
            'success': True,
            'report_id': report_id,
            'failed_step': failed_step,
            'compensation_results': results
        }
        
    except Exception as e:
        logger.error(f"Failed to execute compensation actions for report {report_id}: {e}")
        raise FatalError(f"Compensation execution error: {e}")


@celery_app.task(base=BaseWorkflowTask, bind=True, queue='workflow_queue')
def handle_dead_letter_task(self, task_id: str, task_name: str, args: List, kwargs: Dict, 
                           error_info: Dict, max_retries_exceeded: bool = True):
    """
    处理死信队列任务
    
    Args:
        task_id: 任务ID
        task_name: 任务名称
        args: 任务参数
        kwargs: 任务关键字参数
        error_info: 错误信息
        max_retries_exceeded: 是否超过最大重试次数
    """
    try:
        logger.error(f"Handling dead letter task: {task_name} ({task_id})")
        
        report_id = kwargs.get('report_id') or (args[0] if args else None)
        
        if not report_id:
            logger.error(f"Cannot extract report_id from dead letter task {task_id}")
            return {'success': False, 'error': 'Missing report_id'}
        
        # 记录死信任务
        _record_dead_letter_task(task_id, task_name, report_id, error_info, max_retries_exceeded)
        
        # 执行补偿操作
        if max_retries_exceeded:
            execute_compensation_actions.delay(report_id, task_name, error_info)
        
        # 更新主任务状态
        _update_main_task_status_for_dead_letter(report_id, task_name, error_info)
        
        # 发送告警通知
        _send_dead_letter_alert(task_id, task_name, report_id, error_info)
        
        return {
            'success': True,
            'task_id': task_id,
            'task_name': task_name,
            'report_id': report_id,
            'handled_at': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to handle dead letter task {task_id}: {e}")
        raise FatalError(f"Dead letter handling error: {e}")


def _execute_single_compensation(action: str, params: Dict) -> Dict:
    """执行单个补偿操作"""
    
    compensation_functions = {
        'cleanup_temp_files': _cleanup_temp_files_compensation,
        'reset_task_status': _reset_task_status_compensation,
        'clear_ocr_cache': _clear_ocr_cache_compensation,
        'cleanup_partial_results': _cleanup_partial_results_compensation,
        'cleanup_api_resources': _cleanup_api_resources_compensation,
        'cleanup_vector_data': _cleanup_vector_data_compensation
    }
    
    func = compensation_functions.get(action)
    if not func:
        raise ValueError(f"Unknown compensation action: {action}")
    
    return func(**params)


def _cleanup_temp_files_compensation(report_id: str) -> Dict:
    """清理临时文件的补偿操作"""
    try:
        import os
        import glob
        
        cleaned_files = []
        temp_patterns = [
            f"/tmp/*{report_id}*",
            f"/data/temp/*{report_id}*",
            f"/data/reports/temp/*{report_id}*"
        ]
        
        for pattern in temp_patterns:
            for file_path in glob.glob(pattern):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        cleaned_files.append(file_path)
                except Exception as e:
                    logger.warning(f"Failed to remove temp file {file_path}: {e}")
        
        return {'cleaned_files': cleaned_files, 'count': len(cleaned_files)}
        
    except Exception as e:
        logger.error(f"Temp files cleanup compensation failed: {e}")
        raise


def _reset_task_status_compensation(report_id: str, step: str) -> Dict:
    """重置任务状态的补偿操作"""
    try:
        from app.models import get_db, TaskStep
        
        db = next(get_db())
        try:
            # 删除失败的任务步骤记录
            deleted_count = db.query(TaskStep).filter(
                TaskStep.report_id == report_id,
                TaskStep.step_name.contains(step)
            ).delete(synchronize_session=False)
            
            db.commit()
            
            return {'reset_steps': deleted_count}
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Task status reset compensation failed: {e}")
        raise


def _clear_ocr_cache_compensation(report_id: str) -> Dict:
    """清理OCR缓存的补偿操作"""
    try:
        from app.services.report_task_processor import get_task_processor
        
        processor = get_task_processor()
        if hasattr(processor, '_ocr_cache') and report_id in processor._ocr_cache:
            del processor._ocr_cache[report_id]
            return {'cache_cleared': True}
        
        return {'cache_cleared': False, 'reason': 'Cache entry not found'}
        
    except Exception as e:
        logger.error(f"OCR cache clear compensation failed: {e}")
        raise


def _cleanup_partial_results_compensation(report_id: str, result_type: str) -> Dict:
    """清理部分结果的补偿操作"""
    try:
        from app.models import get_db, TaskResult
        
        db = next(get_db())
        try:
            # 删除指定类型的任务结果
            deleted_count = db.query(TaskResult).filter(
                TaskResult.report_id == report_id,
                TaskResult.result_type == result_type
            ).delete(synchronize_session=False)
            
            db.commit()
            
            return {'deleted_results': deleted_count, 'result_type': result_type}
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Partial results cleanup compensation failed: {e}")
        raise


def _cleanup_api_resources_compensation(report_id: str) -> Dict:
    """清理API资源的补偿操作"""
    try:
        # 这里可以添加具体的API资源清理逻辑
        # 例如：撤销API调用、清理外部系统中的数据等
        
        logger.info(f"Cleaning up API resources for report {report_id}")
        
        # 示例：清理外部API调用记录
        cleanup_count = 0
        
        return {'cleanup_count': cleanup_count, 'resource_type': 'api'}
        
    except Exception as e:
        logger.error(f"API resources cleanup compensation failed: {e}")
        raise


def _cleanup_vector_data_compensation(report_id: str) -> Dict:
    """清理向量数据的补偿操作"""
    try:
        # 这里可以添加具体的向量数据清理逻辑
        # 例如：从向量数据库中删除相关数据
        
        logger.info(f"Cleaning up vector data for report {report_id}")
        
        # 示例：清理向量数据库中的记录
        cleanup_count = 0
        
        return {'cleanup_count': cleanup_count, 'resource_type': 'vector'}
        
    except Exception as e:
        logger.error(f"Vector data cleanup compensation failed: {e}")
        raise


def _record_compensation_execution(report_id: str, failed_step: str, results: List[Dict], error_details: Dict = None):
    """记录补偿操作执行情况"""
    try:
        from app.models import get_db, TaskStep
        
        db = next(get_db())
        try:
            compensation_step = TaskStep(
                report_id=report_id,
                step_name=f'compensation_{failed_step}',
                status='completed',
                start_time=datetime.utcnow(),
                end_time=datetime.utcnow(),
                duration=0,
                result=str(results)
            )
            
            db.add(compensation_step)
            db.commit()
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to record compensation execution: {e}")


def _record_dead_letter_task(task_id: str, task_name: str, report_id: str, error_info: Dict, max_retries_exceeded: bool):
    """记录死信任务"""
    try:
        from app.models import get_db, TaskError
        
        db = next(get_db())
        try:
            dead_letter_error = TaskError(
                report_id=report_id,
                task_name=f'DEAD_LETTER_{task_name}',
                error_type='DeadLetterTask',
                error_message=f"Task failed permanently: {error_info.get('error', 'Unknown error')}",
                traceback=error_info.get('traceback', ''),
                celery_task_id=task_id,
                retry_count=error_info.get('retries', 0)
            )
            
            db.add(dead_letter_error)
            db.commit()
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to record dead letter task: {e}")


def _update_main_task_status_for_dead_letter(report_id: str, task_name: str, error_info: Dict):
    """为死信任务更新主任务状态"""
    try:
        from app.models import get_db, ReportTask
        
        db = next(get_db())
        try:
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
            if task:
                task.status = 'failed'
                task.error_message = f"Dead letter task: {task_name} - {error_info.get('error', 'Unknown error')}"
                task.error_step = task_name
                task.updated_at = datetime.utcnow()
                
                db.commit()
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Failed to update main task status for dead letter: {e}")


def _send_dead_letter_alert(task_id: str, task_name: str, report_id: str, error_info: Dict):
    """发送死信任务告警"""
    try:
        # 这里可以集成告警系统，例如：
        # - 发送邮件通知
        # - 发送Slack消息
        # - 调用监控系统API
        
        alert_message = f"""
        Dead Letter Task Alert:
        - Task ID: {task_id}
        - Task Name: {task_name}
        - Report ID: {report_id}
        - Error: {error_info.get('error', 'Unknown error')}
        - Time: {datetime.utcnow().isoformat()}
        """
        
        logger.critical(alert_message)
        
        # 示例：这里可以添加实际的告警发送逻辑
        
    except Exception as e:
        logger.error(f"Failed to send dead letter alert: {e}")