"""
任务状态查询服务
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional

from sqlalchemy.orm import Session
from app.models import get_db, ReportTask, TaskStep, TaskResult, TaskError, TaskStatus
from app.schemas.report_task import TaskStatusResponse, StepDetail

logger = logging.getLogger(__name__)


class TaskStatusService:
    """任务状态查询服务"""
    
    def __init__(self):
        pass
    
    def get_task_status(self, report_id: str) -> Optional[Dict]:
        """获取任务的综合状态信息"""
        try:
            db = next(get_db())
            try:
                # 获取主任务信息
                task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
                
                if not task:
                    return None
                
                # 获取任务步骤信息
                steps = db.query(TaskStep).filter(TaskStep.report_id == report_id).all()
                
                # 获取任务结果信息
                results = db.query(TaskResult).filter(TaskResult.report_id == report_id).all()
                
                # 获取任务错误信息
                errors = db.query(TaskError).filter(TaskError.report_id == report_id).all()
                
                # 构建步骤详情
                step_details = self._build_step_details(task, steps)
                
                # 计算进度百分比
                progress_percent = self._calculate_progress(task, steps)
                
                # 构建综合状态响应
                status_info = {
                    "report_id": task.report_id,
                    "report_key": task.report_key,
                    "status": task.status.value,
                    "workflow_id": task.workflow_id,
                    "progress_percent": progress_percent,
                    "current_step": task.current_step,
                    "total_steps": task.total_steps,
                    "retry_count": task.retry_count,
                    "error_message": task.error_message,
                    "error_step": task.error_step,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                    "started_at": task.started_at.isoformat() if task.started_at else None,
                    "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                    "step_details": step_details,
                    "results_summary": self._build_results_summary(results),
                    "error_history": self._build_error_history(errors),
                    "processing_time": self._calculate_processing_time(task),
                    "estimated_completion": self._estimate_completion_time(task, steps)
                }
                
                return status_info
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to get task status for {report_id}: {e}")
            return None
    
    def get_task_list(self, status: Optional[str] = None, page: int = 1, 
                     size: int = 20, sort_by: str = "created_at", 
                     sort_order: str = "desc") -> Dict:
        """获取任务列表"""
        try:
            db = next(get_db())
            try:
                # 构建查询
                query = db.query(ReportTask)
                
                # 状态过滤
                if status:
                    try:
                        status_enum = TaskStatus(status)
                        query = query.filter(ReportTask.status == status_enum)
                    except ValueError:
                        logger.warning(f"Invalid status filter: {status}")
                
                # 排序
                if sort_by == "created_at":
                    order_column = ReportTask.created_at
                elif sort_by == "updated_at":
                    order_column = ReportTask.updated_at
                elif sort_by == "status":
                    order_column = ReportTask.status
                else:
                    order_column = ReportTask.created_at
                
                if sort_order.lower() == "desc":
                    query = query.order_by(order_column.desc())
                else:
                    query = query.order_by(order_column.asc())
                
                # 获取总数
                total = query.count()
                
                # 分页
                offset = (page - 1) * size
                tasks = query.offset(offset).limit(size).all()
                
                # 构建任务列表
                task_list = []
                for task in tasks:
                    # 获取每个任务的步骤统计
                    steps = db.query(TaskStep).filter(TaskStep.report_id == task.report_id).all()
                    
                    task_info = {
                        "report_id": task.report_id,
                        "status": task.status.value,
                        "workflow_id": task.workflow_id,
                        "progress_percent": self._calculate_progress(task, steps),
                        "retry_count": task.retry_count,
                        "created_at": task.created_at.isoformat() if task.created_at else None,
                        "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                        "processing_time": self._calculate_processing_time(task),
                        "step_summary": {
                            "total": len(steps),
                            "completed": len([s for s in steps if s.status == 'completed']),
                            "failed": len([s for s in steps if s.status == 'failed']),
                            "processing": len([s for s in steps if s.status == 'processing'])
                        }
                    }
                    task_list.append(task_info)
                
                # 计算分页信息
                pages = (total + size - 1) // size
                
                return {
                    "tasks": task_list,
                    "total": total,
                    "page": page,
                    "size": size,
                    "pages": pages,
                    "has_next": page < pages,
                    "has_prev": page > 1
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to get task list: {e}")
            return {
                "tasks": [],
                "total": 0,
                "page": page,
                "size": size,
                "pages": 0,
                "has_next": False,
                "has_prev": False,
                "error": str(e)
            }
    
    def get_task_statistics(self) -> Dict:
        """获取任务统计信息"""
        try:
            db = next(get_db())
            try:
                # 按状态统计任务数量
                status_stats = {}
                for status in TaskStatus:
                    count = db.query(ReportTask).filter(ReportTask.status == status).count()
                    status_stats[status.value] = count
                
                # 获取总任务数
                total_tasks = db.query(ReportTask).count()
                
                # 获取今日任务数
                today = datetime.utcnow().date()
                today_tasks = db.query(ReportTask).filter(
                    ReportTask.created_at >= today
                ).count()
                
                # 获取平均处理时间
                completed_tasks = db.query(ReportTask).filter(
                    ReportTask.status == TaskStatus.COMPLETED,
                    ReportTask.started_at.isnot(None),
                    ReportTask.completed_at.isnot(None)
                ).all()
                
                avg_processing_time = 0
                if completed_tasks:
                    total_time = sum([
                        (task.completed_at - task.started_at).total_seconds()
                        for task in completed_tasks
                    ])
                    avg_processing_time = total_time / len(completed_tasks)
                
                # 获取错误统计
                error_count = db.query(TaskError).count()
                
                # 获取重试统计
                retry_tasks = db.query(ReportTask).filter(ReportTask.retry_count > 0).count()
                
                return {
                    "total_tasks": total_tasks,
                    "today_tasks": today_tasks,
                    "status_distribution": status_stats,
                    "avg_processing_time_seconds": avg_processing_time,
                    "total_errors": error_count,
                    "tasks_with_retries": retry_tasks,
                    "success_rate": (status_stats.get('completed', 0) / total_tasks * 100) if total_tasks > 0 else 0,
                    "failure_rate": (status_stats.get('failed', 0) / total_tasks * 100) if total_tasks > 0 else 0
                }
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to get task statistics: {e}")
            return {"error": str(e)}
    
    def _build_step_details(self, task: ReportTask, steps: List[TaskStep]) -> List[Dict]:
        """构建步骤详情列表"""
        step_details = []
        
        # 创建步骤映射
        step_map = {step.step_name: step for step in steps}
        
        # 定义标准步骤顺序
        standard_steps = [
            "app.tasks.ocr_tasks.ocr_task",
            "app.tasks.ai_tasks.meta_analysis_task",
            "app.tasks.ai_tasks.tabular_analysis_task",
            "app.tasks.ai_tasks.overall_analysis_task",
            "app.tasks.ai_tasks.image_analysis_task",
            "app.tasks.ai_tasks.diseases_extraction_task",
            "app.tasks.post_process_tasks.translation_task",
            "app.tasks.post_process_tasks.indicator_conversion_task",
            "app.tasks.storage_tasks.call_api_task",
            "app.tasks.storage_tasks.store_vector_task",
            "app.tasks.workflow_tasks.finalize_workflow"
        ]
        
        for i, step_name in enumerate(standard_steps):
            step = step_map.get(step_name)
            
            if step:
                step_detail = {
                    "step_index": i,
                    "step_name": step_name.split('.')[-1],  # 只显示任务名称
                    "full_step_name": step_name,
                    "status": step.status,
                    "celery_task_id": step.celery_task_id,
                    "start_time": step.start_time.isoformat() if step.start_time else None,
                    "end_time": step.end_time.isoformat() if step.end_time else None,
                    "duration": step.duration,
                    "error_message": step.error_message,
                    "completed": step.status == 'completed'
                }
            else:
                # 未开始的步骤
                step_detail = {
                    "step_index": i,
                    "step_name": step_name.split('.')[-1],
                    "full_step_name": step_name,
                    "status": "pending",
                    "celery_task_id": None,
                    "start_time": None,
                    "end_time": None,
                    "duration": None,
                    "error_message": None,
                    "completed": False
                }
            
            step_details.append(step_detail)
        
        return step_details
    
    def _build_results_summary(self, results: List[TaskResult]) -> Dict:
        """构建结果摘要"""
        results_summary = {
            "total_results": len(results),
            "result_types": {},
            "latest_results": []
        }
        
        # 按类型统计结果
        for result in results:
            result_type = result.result_type
            if result_type not in results_summary["result_types"]:
                results_summary["result_types"][result_type] = 0
            results_summary["result_types"][result_type] += 1
        
        # 获取最新的几个结果
        sorted_results = sorted(results, key=lambda x: x.created_at, reverse=True)
        for result in sorted_results[:5]:  # 最新5个结果
            results_summary["latest_results"].append({
                "result_type": result.result_type,
                "file_path": result.file_path,
                "created_at": result.created_at.isoformat() if result.created_at else None
            })
        
        return results_summary
    
    def _build_error_history(self, errors: List[TaskError]) -> List[Dict]:
        """构建错误历史"""
        error_history = []
        
        for error in sorted(errors, key=lambda x: x.created_at, reverse=True):
            error_history.append({
                "task_name": error.task_name,
                "error_type": error.error_type,
                "error_message": error.error_message,
                "retry_count": error.retry_count,
                "created_at": error.created_at.isoformat() if error.created_at else None
            })
        
        return error_history[:10]  # 最新10个错误
    
    def _calculate_progress(self, task: ReportTask, steps: List[TaskStep]) -> float:
        """计算任务进度百分比"""
        if not steps:
            return float(task.progress_percent) if task.progress_percent else 0.0
        
        completed_steps = len([s for s in steps if s.status == 'completed'])
        total_steps = len(steps)
        
        if total_steps == 0:
            return 0.0
        
        return (completed_steps / total_steps) * 100.0
    
    def _calculate_processing_time(self, task: ReportTask) -> Optional[int]:
        """计算处理时间（秒）"""
        if task.started_at:
            end_time = task.completed_at or datetime.utcnow()
            return int((end_time - task.started_at).total_seconds())
        return None
    
    def _estimate_completion_time(self, task: ReportTask, steps: List[TaskStep]) -> Optional[str]:
        """估算完成时间"""
        if task.status == TaskStatus.COMPLETED:
            return None
        
        if not task.started_at or not steps:
            return None
        
        completed_steps = [s for s in steps if s.status == 'completed']
        if not completed_steps:
            return None
        
        # 计算平均每步耗时
        total_duration = sum([s.duration for s in completed_steps if s.duration])
        avg_step_duration = total_duration / len(completed_steps) if completed_steps else 60
        
        # 估算剩余步骤数
        remaining_steps = len([s for s in steps if s.status in ['pending', 'processing']])
        
        # 估算剩余时间
        estimated_seconds = remaining_steps * avg_step_duration
        estimated_completion = datetime.utcnow().timestamp() + estimated_seconds
        
        return datetime.fromtimestamp(estimated_completion).isoformat()


# 全局任务状态服务实例
_task_status_service: Optional[TaskStatusService] = None


def get_task_status_service() -> TaskStatusService:
    """获取任务状态服务实例（单例模式）"""
    global _task_status_service
    if _task_status_service is None:
        _task_status_service = TaskStatusService()
    return _task_status_service