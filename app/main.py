from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api import api_router
from app.api.playground import playground_router
from app.exceptions.handlers import setup_exception_handlers
from app.middleware.logging import LoggingMiddleware
from app.middleware.traceid import TraceIDMiddleware
from app.schemas.response import ApiResponse
from app.services.report_task_manager import get_task_manager
from app.core.worker_manager import get_worker_manager
from app.core.task_poller import get_task_poller
from app.core.flower_config import get_flower_manager
from app.core.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)


# 定义应用的生命周期管理
@asynccontextmanager
async def app_lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 Application startup!")

    try:
        # 启动Celery Worker管理器
        worker_manager = get_worker_manager()
        worker_manager.start_workers()
        
        # 启动任务轮询器
        task_poller = get_task_poller()
        task_poller.start_polling()
        
        # 启动Flower监控（可选）
        flower_enabled = getattr(settings, 'FLOWER_ENABLED', True)
        if flower_enabled:
            try:
                flower_manager = get_flower_manager()
                flower_manager.start_flower()
                logger.info("Flower monitoring started")
            except Exception as e:
                logger.warning(f"Failed to start Flower monitoring: {e}")
        
        # 启动任务管理器工作线程（保持向后兼容）
        task_manager = get_task_manager()
        await task_manager.start_workers(num_workers=3)

        logger.info("应用初始化完成 - Celery workers, task poller, Flower monitoring and task manager started")

    except Exception as e:
        logger.error("应用初始化失败", error=str(e))
        raise

    yield

    # 关闭时执行
    logger.info("Shutting down...")

    # 停止任务轮询器
    try:
        task_poller = get_task_poller()
        task_poller.stop_polling()
        logger.info("Task poller stopped successfully")
    except Exception as e:
        logger.error("Failed to stop task poller", error=str(e))

    # 停止Flower监控
    try:
        flower_manager = get_flower_manager()
        flower_manager.stop_flower()
        logger.info("Flower monitoring stopped successfully")
    except Exception as e:
        logger.error("Failed to stop Flower monitoring", error=str(e))

    # 停止Celery Worker管理器
    try:
        worker_manager = get_worker_manager()
        worker_manager.stop_workers()
        logger.info("Celery workers stopped successfully")
    except Exception as e:
        logger.error("Failed to stop Celery workers", error=str(e))

    # 关闭客户端管理器
    try:
        if hasattr(app.state, "client_manager"):
            await app.state.client_manager.close()
            logger.info("Client manager closed successfully")
    except Exception as e:
        logger.error("Failed to close client manager", error=str(e))


app = FastAPI(lifespan=app_lifespan)


# 设置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://app.agno.com",
        "http://localhost:3000",
        "http://localhost:8080",
    ],  # 指定具体的域名以支持credentials
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加自定义中间件
app.add_middleware(LoggingMiddleware)
app.add_middleware(TraceIDMiddleware)

# 设置异常处理器
setup_exception_handlers(app)


# 健康检查接口（简单版本，保持向后兼容）
@app.get("/health")
async def simple_health_check():
    """
    简单健康检查接口（保持向后兼容）

    Returns:
        dict: 服务状态信息
    """
    return ApiResponse.success(data={"status": "healthy", "service": "ai-assistant", "version": "2.0.0-optimized"})


# Celery Worker状态监控接口
@app.get("/workers/status")
async def get_workers_status():
    """
    获取Celery Worker状态信息

    Returns:
        dict: Worker状态信息
    """
    try:
        worker_manager = get_worker_manager()
        status = worker_manager.get_worker_status()
        return ApiResponse.success(data=status)
    except Exception as e:
        logger.error(f"Failed to get worker status: {e}")
        return ApiResponse.error(message=f"Failed to get worker status: {e}")


@app.get("/workers/health")
async def get_workers_health():
    """
    获取Celery Worker健康检查信息

    Returns:
        dict: Worker健康状态
    """
    try:
        worker_manager = get_worker_manager()
        health = worker_manager.health_check()
        return ApiResponse.success(data=health)
    except Exception as e:
        logger.error(f"Failed to get worker health: {e}")
        return ApiResponse.error(message=f"Failed to get worker health: {e}")


@app.post("/workers/{worker_name}/restart")
async def restart_worker(worker_name: str):
    """
    重启指定的Worker

    Args:
        worker_name: Worker名称

    Returns:
        dict: 重启结果
    """
    try:
        worker_manager = get_worker_manager()
        success = worker_manager.restart_worker(worker_name)
        
        if success:
            return ApiResponse.success(data={"message": f"Worker {worker_name} restarted successfully"})
        else:
            return ApiResponse.error(message=f"Failed to restart worker {worker_name}")
            
    except Exception as e:
        logger.error(f"Failed to restart worker {worker_name}: {e}")
        return ApiResponse.error(message=f"Failed to restart worker {worker_name}: {e}")


# 任务轮询器监控接口
@app.get("/poller/status")
async def get_poller_status():
    """
    获取任务轮询器状态信息

    Returns:
        dict: 轮询器状态信息
    """
    try:
        task_poller = get_task_poller()
        stats = task_poller.get_stats()
        return ApiResponse.success(data=stats)
    except Exception as e:
        logger.error(f"Failed to get poller status: {e}")
        return ApiResponse.error(message=f"Failed to get poller status: {e}")


@app.post("/poller/force-poll")
async def force_poll():
    """
    强制执行一次任务轮询

    Returns:
        dict: 轮询结果
    """
    try:
        task_poller = get_task_poller()
        result = task_poller.force_poll()
        return ApiResponse.success(data=result)
    except Exception as e:
        logger.error(f"Failed to force poll: {e}")
        return ApiResponse.error(message=f"Failed to force poll: {e}")


# Flower监控接口
@app.get("/flower/status")
async def get_flower_status():
    """
    获取Flower监控状态

    Returns:
        dict: Flower状态信息
    """
    try:
        flower_manager = get_flower_manager()
        status = flower_manager.get_status()
        return ApiResponse.success(data=status)
    except Exception as e:
        logger.error(f"Failed to get Flower status: {e}")
        return ApiResponse.error(message=f"Failed to get Flower status: {e}")


@app.post("/flower/restart")
async def restart_flower():
    """
    重启Flower监控服务

    Returns:
        dict: 重启结果
    """
    try:
        flower_manager = get_flower_manager()
        flower_manager.restart_flower()
        return ApiResponse.success(data={"message": "Flower restarted successfully"})
    except Exception as e:
        logger.error(f"Failed to restart Flower: {e}")
        return ApiResponse.error(message=f"Failed to restart Flower: {e}")


# 注册路由
app.include_router(api_router, prefix="/api/v1")
app.include_router(playground_router, prefix="/v1")
