"""火山引擎知识库服务封装

提供知识库的创建、管理、搜索等功能，使用单例模式避免重复初始化。
"""

import threading
from typing import Any, Optional

from structlog import get_logger
from volcengine.viking_knowledgebase import VikingKnowledgeBaseService
from volcengine.viking_knowledgebase import Collection, Doc
from volcengine.viking_knowledgebase.Point import Point

from app.client.client_manager import get_volcengine_client
from app.core.config import settings
from app.schemas.knowledgebase import CollectionInfo, OperationResponse

logger = get_logger(__name__)


class VikingKnowledgeBaseClient:
    """火山引擎知识库客户端（线程安全单例）"""

    _instance: Optional["VikingKnowledgeBaseClient"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "VikingKnowledgeBaseClient":
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化客户端（只执行一次）"""
        if not self._initialized:
            self._service: VikingKnowledgeBaseService | None = None
            self._default_collection: Collection | None = None
            self._setup_service()
            self._setup_default_collection()
            self._initialized = True

    def _setup_service(self) -> None:
        """设置知识库服务"""
        try:
            # 获取火山引擎客户端配置
            volcengine_client = get_volcengine_client()
            config = volcengine_client.get_configuration()

            if not config:
                raise ValueError("Volcengine client configuration is not available")

            # 创建知识库服务
            self._service = VikingKnowledgeBaseService(
                host=settings.VOLCENGINE_KB_HOST,
                scheme=settings.VOLCENGINE_KB_SCHEME,
                ak=config.ak,
                sk=config.sk,
                connection_timeout=settings.VOLCENGINE_KB_CONNECTION_TIMEOUT,
                socket_timeout=settings.VOLCENGINE_KB_SOCKET_TIMEOUT,
            )

            logger.info(
                "Viking knowledge base service initialized successfully",
                host=settings.VOLCENGINE_KB_HOST,
                scheme=settings.VOLCENGINE_KB_SCHEME,
            )

        except Exception as e:
            logger.error("Failed to initialize Viking knowledge base service", error=str(e))
            raise

    def get_service(self) -> VikingKnowledgeBaseService:
        """获取知识库服务实例"""
        if self._service is None:
            raise RuntimeError("Knowledge base service not initialized")
        return self._service

    def search_collection(
        self,
        collection_name: str,
        query: str,
        limit: int = 10,
        dense_weight: float = 0.7,
        rerank_switch: bool = True,
        doc_filter: dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> list[Point]:
        """搜索知识库

        Args:
            collection_name: 知识库名称
            query: 搜索查询文本
            limit: 返回结果数量 (1-200)
            dense_weight: 向量检索权重 (0.2-1.0)
            rerank_switch: 是否启用重排序
            doc_filter: 文档过滤条件

        Returns:
            List[KnowledgePoint]: 知识点列表
        """
        try:
            service = self.get_service()

            return service.search_collection(
                collection_name=collection_name,
                query=query,
                limit=limit,
                dense_weight=dense_weight,
                rerank_switch=rerank_switch,
            )
        except Exception as e:
            logger.error(
                "Failed to search knowledge base",
                collection_name=collection_name,
                query=query[:100] + "..." if len(query) > 100 else query,
                error=str(e),
            )
            raise

    def list_collections(self) -> list[Collection]:
        """列出知识库

        Returns:
            List[CollectionInfo]: 知识库列表
        """
        # 调用列表接口
        return self.get_service().list_collections()

    def _setup_default_collection(self) -> None:
        """设置默认的 hdl collection"""
        try:
            collections = self.list_collections()
            
            # 查找名为 hdl 的 collection
            for collection in collections:
                if collection.collection_name == "hdl":
                    self._default_collection = collection
                    logger.info("Found default collection 'hdl'", collection_name=collection.collection_name)
                    return
            
            logger.warning("Default collection 'hdl' not found")
            
        except Exception as e:
            logger.error("Failed to setup default collection", error=str(e))
            # 不抛出异常，允许客户端继续工作

    def get_default_collection(self) -> Collection:
        """获取默认 collection"""
        if self._default_collection is None:
            raise RuntimeError("Default collection 'hdl' not available")
        return self._default_collection

    def list_docs(self) -> list[Doc]:
        """列出文档 """
        return self.get_default_collection().list_docs(project="default")

    @classmethod
    def get_instance(cls) -> "VikingKnowledgeBaseClient":
        """获取单例实例"""
        return cls()


# 便捷的工厂函数和快捷接口
def get_knowledgebase_client() -> VikingKnowledgeBaseClient:
    """获取知识库客户端单例"""
    return VikingKnowledgeBaseClient.get_instance()


def search_knowledgebase(
    collection_name: str,
    query: str,
    limit: int = 20,
    dense_weight: float = 0.7,
    rerank_switch: bool = True,
    doc_filter: dict[str, Any] | None = None,
    **kwargs: Any,
) -> list[Point]:
    """搜索知识库（简化接口）

    Args:
        collection_name: 知识库名称
        query: 搜索查询文本
        limit: 返回结果数量
        dense_weight: 向量检索权重
        rerank_switch: 是否启用重排序
        doc_filter: 文档过滤条件

    Returns:
        List[KnowledgePoint]: 知识点列表
    """
    client = get_knowledgebase_client()
    return client.search_collection(
        collection_name=collection_name,
        query=query,
        limit=limit,
        dense_weight=dense_weight,
        rerank_switch=rerank_switch,
        doc_filter=doc_filter,
        **kwargs,
    )


