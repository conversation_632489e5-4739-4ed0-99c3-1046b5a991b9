"""火山引擎 TOS 对象存储客户端封装

提供简单易用的 TOS 上传功能，支持：
- 字符流上传
- Bytes 流上传
- 网络流上传
- 本地文件上传
- 异步上传和并发控制
- 文件下载（本地和内存）
"""

import asyncio
from concurrent.futures import ThreadPoolExecutor
from io import BytesIO, StringIO
from pathlib import Path
import threading
from typing import Any, Optional

from structlog import get_logger
import tos

from app.core.config import settings

logger = get_logger(__name__)


class TosClient:
    """火山引擎 TOS 客户端（线程安全单例）"""

    _instance: Optional["TosClient"] = None
    _lock = threading.Lock()

    def __new__(cls) -> "TosClient":
        """线程安全的单例实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """初始化配置（只执行一次）"""
        if not self._initialized:
            self._client: tos.TosClientV2 | None = None
            self._semaphore = asyncio.Semaphore(settings.TOS_MAX_CONCURRENCY)
            self._executor = ThreadPoolExecutor(max_workers=settings.TOS_MAX_CONCURRENCY)
            self._setup_client()
            self._initialized = True

    def _setup_client(self) -> None:
        """设置 TOS 客户端配置"""
        if not settings.VOLCENGINE_ACCESS_KEY or not settings.VOLCENGINE_ACCESS_KEY:
            raise ValueError("VOLCENGINE_ACCESS_KEY and VOLCENGINE_ACCESS_KEY must be set")

        if not settings.TOS_BUCKET_NAME:
            raise ValueError("TOS_BUCKET_NAME must be set")

        try:
            self._client = tos.TosClientV2(
                ak=settings.VOLCENGINE_ACCESS_KEY,
                sk=settings.VOLCENGINE_SECRET_KEY,
                endpoint=settings.TOS_ENDPOINT,
                region=settings.TOS_REGION,
                connection_time=settings.TOS_CONNECTION_TIME,
                socket_timeout=settings.TOS_READ_TIMEOUT,
                max_retry_count=3,  # 设置超时重试次数为3次。重试策略为指数避让策略，基准时间为100ms
                max_connections=1024,  # 连接池大小
            )

            logger.info(
                "TOS client initialized successfully",
                endpoint=settings.TOS_ENDPOINT,
                region=settings.TOS_REGION,
                bucket=settings.TOS_BUCKET_NAME,
            )

        except Exception as e:
            logger.error("Failed to initialize TOS client", error=str(e))
            raise

    def upload_string(
        self, content: str, object_key: str, bucket_name: str | None = None, content_type: str = "text/plain", **kwargs
    ) -> dict[str, Any]:
        """上传字符串内容

        Args:
            content: 要上传的字符串内容
            object_key: 对象键（文件路径）
            bucket_name: 存储桶名称，默认使用配置的桶名
            content_type: 内容类型
            **kwargs: 其他 TOS 参数

        Returns:
            上传结果
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        try:
            result = self._client.put_object(
                bucket=bucket_name, key=object_key, content=content, content_type=content_type, **kwargs
            )

            logger.info(
                "String uploaded successfully",
                bucket=bucket_name,
                key=object_key,
                size=len(content),
                etag=result.etag,
            )

            return {
                "success": True,
                "bucket": bucket_name,
                "key": object_key,
                "etag": result.etag,
                "size": len(content),
            }

        except Exception as e:
            logger.error(
                "Failed to upload string",
                bucket=bucket_name,
                key=object_key,
                error=str(e),
            )
            raise

    def upload_bytes(
        self,
        content: bytes,
        object_key: str,
        bucket_name: str | None = None,
        content_type: str = "application/octet-stream",
        **kwargs,
    ) -> dict[str, Any]:
        """上传 bytes 内容

        Args:
            content: 要上传的 bytes 内容
            object_key: 对象键（文件路径）
            bucket_name: 存储桶名称，默认使用配置的桶名
            content_type: 内容类型
            **kwargs: 其他 TOS 参数

        Returns:
            上传结果
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        try:
            result = self._client.put_object(
                bucket=bucket_name, key=object_key, content=content, content_type=content_type, **kwargs
            )

            logger.info(
                "Bytes uploaded successfully",
                bucket=bucket_name,
                key=object_key,
                size=len(content),
                etag=result.etag,
            )

            return {
                "success": True,
                "bucket": bucket_name,
                "key": object_key,
                "etag": result.etag,
                "size": len(content),
            }

        except Exception as e:
            logger.error(
                "Failed to upload bytes",
                bucket=bucket_name,
                key=object_key,
                error=str(e),
            )
            raise

    def upload_stream(
        self,
        stream: BytesIO | StringIO,
        object_key: str,
        bucket_name: str | None = None,
        content_type: str = "application/octet-stream",
        **kwargs,
    ) -> dict[str, Any]:
        """上传流内容

        Args:
            stream: 要上传的流对象
            object_key: 对象键（文件路径）
            bucket_name: 存储桶名称，默认使用配置的桶名
            content_type: 内容类型
            **kwargs: 其他 TOS 参数

        Returns:
            上传结果
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        try:
            # 获取流的当前位置并移动到开始
            original_position = stream.tell()
            stream.seek(0)

            result = self._client.put_object(
                bucket=bucket_name, key=object_key, content=stream, content_type=content_type, **kwargs
            )

            # 恢复流的原始位置
            stream.seek(original_position)

            logger.info(
                "Stream uploaded successfully",
                bucket=bucket_name,
                key=object_key,
                etag=result.etag,
            )

            return {
                "success": True,
                "bucket": bucket_name,
                "key": object_key,
                "etag": result.etag,
            }

        except Exception as e:
            logger.error(
                "Failed to upload stream",
                bucket=bucket_name,
                key=object_key,
                error=str(e),
            )
            raise

    def upload_file(
        self,
        file_path: str | Path,
        object_key: str | None = None,
        bucket_name: str | None = None,
        content_type: str | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """上传本地文件

        Args:
            file_path: 本地文件路径
            object_key: 对象键（文件路径），默认使用文件名
            bucket_name: 存储桶名称，默认使用配置的桶名
            content_type: 内容类型，默认根据文件扩展名自动识别
            **kwargs: 其他 TOS 参数

        Returns:
            上传结果
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME
        object_key = object_key or file_path.name

        # 自动识别内容类型
        if content_type is None:
            import mimetypes

            content_type, _ = mimetypes.guess_type(str(file_path))
            content_type = content_type or "application/octet-stream"

        try:
            result = self._client.put_object_from_file(
                bucket=bucket_name, key=object_key, file_path=str(file_path), content_type=content_type, **kwargs
            )

            file_size = file_path.stat().st_size

            logger.info(
                "File uploaded successfully",
                bucket=bucket_name,
                key=object_key,
                file_path=str(file_path),
                size=file_size,
                etag=result.etag,
            )

            return {
                "success": True,
                "bucket": bucket_name,
                "key": object_key,
                "file_path": str(file_path),
                "etag": result.etag,
                "size": file_size,
            }

        except Exception as e:
            logger.error(
                "Failed to upload file",
                bucket=bucket_name,
                key=object_key,
                file_path=str(file_path),
                error=str(e),
            )
            raise

    async def async_upload_string(
        self, content: str, object_key: str, bucket_name: str | None = None, content_type: str = "text/plain", **kwargs
    ) -> dict[str, Any]:
        """异步上传字符串内容"""
        async with self._semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor, self.upload_string, content, object_key, bucket_name, content_type, **kwargs
            )

    async def async_upload_bytes(
        self,
        content: bytes,
        object_key: str,
        bucket_name: str | None = None,
        content_type: str = "application/octet-stream",
        **kwargs,
    ) -> dict[str, Any]:
        """异步上传 bytes 内容"""
        async with self._semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor, self.upload_bytes, content, object_key, bucket_name, content_type, **kwargs
            )

    async def async_upload_stream(
        self,
        stream: BytesIO | StringIO,
        object_key: str,
        bucket_name: str | None = None,
        content_type: str = "application/octet-stream",
        **kwargs,
    ) -> dict[str, Any]:
        """异步上传流内容"""
        async with self._semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor, self.upload_stream, stream, object_key, bucket_name, content_type, **kwargs
            )

    async def async_upload_file(
        self,
        file_path: str | Path,
        object_key: str | None = None,
        bucket_name: str | None = None,
        content_type: str | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """异步上传本地文件"""
        async with self._semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor, self.upload_file, file_path, object_key, bucket_name, content_type, **kwargs
            )

    async def async_upload_folder(
        self, folder_path: str | Path, object_prefix: str = "", bucket_name: str | None = None, **kwargs
    ) -> dict[str, Any]:
        """异步上传文件夹（简单版本）

        Args:
            folder_path: 本地文件夹路径
            object_prefix: 对象键前缀
            bucket_name: 存储桶名称，默认使用配置的桶名
            **kwargs: 其他 TOS 参数

        Returns:
            上传结果汇总
        """
        folder_path = Path(folder_path)
        if not folder_path.exists() or not folder_path.is_dir():
            raise ValueError(f"Folder not found or not a directory: {folder_path}")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        # 获取所有文件
        files = list(folder_path.rglob("*"))
        files = [f for f in files if f.is_file()]

        if not files:
            return {"success": True, "uploaded_files": 0, "results": []}

        # 并发上传所有文件
        tasks = []
        for file_path in files:
            # 计算相对路径作为对象键
            relative_path = file_path.relative_to(folder_path)
            object_key = f"{object_prefix}{relative_path}".replace("\\", "/")

            task = self.async_upload_file(file_path=file_path, object_key=object_key, bucket_name=bucket_name, **kwargs)
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        successful_uploads = 0
        failed_uploads = 0
        upload_results = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_uploads += 1
                logger.error(
                    "Failed to upload file in folder",
                    file_path=str(files[i]),
                    error=str(result),
                )
                upload_results.append(
                    {
                        "file_path": str(files[i]),
                        "success": False,
                        "error": str(result),
                    }
                )
            else:
                successful_uploads += 1
                upload_results.append(result)

        logger.info(
            "Folder upload completed",
            folder_path=str(folder_path),
            total_files=len(files),
            successful=successful_uploads,
            failed=failed_uploads,
        )

        return {
            "success": failed_uploads == 0,
            "folder_path": str(folder_path),
            "total_files": len(files),
            "successful_uploads": successful_uploads,
            "failed_uploads": failed_uploads,
            "results": upload_results,
        }

    def download_to_file(
        self, object_key: str, file_path: str | Path | None = None, bucket_name: str | None = None, **kwargs
    ) -> dict[str, Any]:
        """下载对象到本地文件

        Args:
            object_key: 对象键（文件路径）
            file_path: 本地文件保存路径，如果不指定则保存到 download 目录
            bucket_name: 存储桶名称，默认使用配置的桶名
            **kwargs: 其他 TOS 参数

        Returns:
            下载结果
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        # 如果未指定文件路径，则使用默认的 download 目录
        if file_path is None:
            download_dir = Path("download")
            download_dir.mkdir(exist_ok=True)
            file_path = download_dir / Path(object_key).name
        else:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            result = self._client.get_object_to_file(
                bucket=bucket_name, key=object_key, file_path=str(file_path), **kwargs
            )

            file_size = file_path.stat().st_size

            logger.info(
                "File downloaded successfully",
                bucket=bucket_name,
                key=object_key,
                file_path=str(file_path),
                size=file_size,
            )

            return {
                "success": True,
                "bucket": bucket_name,
                "key": object_key,
                "file_path": str(file_path),
                "size": file_size,
                "content_type": result.content_type,
                "etag": result.etag,
            }

        except Exception as e:
            logger.error(
                "Failed to download file",
                bucket=bucket_name,
                key=object_key,
                file_path=str(file_path),
                error=str(e),
            )
            raise

    def download_to_memory(self, object_key: str, bucket_name: str | None = None, **kwargs) -> dict[str, Any]:
        """下载对象到内存

        Args:
            object_key: 对象键（文件路径）
            bucket_name: 存储桶名称，默认使用配置的桶名
            **kwargs: 其他 TOS 参数

        Returns:
            下载结果，包含文件内容的 BytesIO 对象
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        try:
            result = self._client.get_object(bucket=bucket_name, key=object_key, **kwargs)

            # 读取所有内容到内存
            content = result.read()
            memory_file = BytesIO(content)
            memory_file.seek(0)  # 重置指针到开始

            logger.info(
                "File downloaded to memory successfully",
                bucket=bucket_name,
                key=object_key,
                size=len(content),
            )

            return {
                "success": True,
                "bucket": bucket_name,
                "key": object_key,
                "size": len(content),
                "content_type": result.content_type,
                "etag": result.etag,
                "content": memory_file,  # BytesIO 对象
            }

        except Exception as e:
            logger.error(
                "Failed to download file to memory",
                bucket=bucket_name,
                key=object_key,
                error=str(e),
            )
            raise

    async def async_download_to_file(
        self, object_key: str, file_path: str | Path | None = None, bucket_name: str | None = None, **kwargs
    ) -> dict[str, Any]:
        """异步下载对象到本地文件"""
        async with self._semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor, self.download_to_file, object_key, file_path, bucket_name, **kwargs
            )

    async def async_download_to_memory(
        self, object_key: str, bucket_name: str | None = None, **kwargs
    ) -> dict[str, Any]:
        """异步下载对象到内存"""
        async with self._semaphore:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._executor, self.download_to_memory, object_key, bucket_name, **kwargs
            )

    def get_object_url(
        self,
        object_key: str,
        bucket_name: str | None = None,
        expires: int = 3600,
        http_method: tos.HttpMethodType = tos.HttpMethodType.Http_Method_Get,
    ) -> str:
        """获取对象的预签名访问链接

        Args:
            object_key: 对象键（文件路径）
            bucket_name: 存储桶名称，默认使用配置的桶名
            expires: 链接有效时间（秒），默认3600秒
            http_method: HTTP 方法类型，默认为 GET

        Returns:
            预签名访问链接
        """
        if not self._client:
            raise RuntimeError("TOS client not initialized")

        bucket_name = bucket_name or settings.TOS_BUCKET_NAME

        try:
            # 生成预签名 URL
            result = self._client.pre_signed_url(http_method, bucket=bucket_name, key=object_key, expires=expires)

            logger.info(
                "Generated pre-signed URL",
                bucket=bucket_name,
                key=object_key,
                expires=expires,
                http_method=http_method,
                url=result.signed_url,
            )

            return result.signed_url

        except Exception as e:
            logger.error(
                "Failed to generate pre-signed URL",
                bucket=bucket_name,
                key=object_key,
                error=str(e),
            )
            raise

    def get_upload_url(self, object_key: str, bucket_name: str | None = None, expires: int = 3600) -> str:
        """获取上传文件的预签名 URL

        Args:
            object_key: 对象键（文件路径）
            bucket_name: 存储桶名称，默认使用配置的桶名
            expires: 链接有效时间（秒），默认3600秒

        Returns:
            用于上传的预签名 URL
        """
        return self.get_object_url(
            object_key=object_key,
            bucket_name=bucket_name,
            expires=expires,
            http_method=tos.HttpMethodType.Http_Method_Get,
        )

    def get_client(self) -> tos.TosClientV2:
        """获取原始 TOS 客户端"""
        if not self._client:
            raise RuntimeError("TOS client not initialized")
        return self._client

    @classmethod
    def get_instance(cls) -> "TosClient":
        """获取单例实例"""
        return cls()


# 便捷的工厂函数
def get_tos_client() -> TosClient:
    """获取 TOS 客户端单例"""
    return TosClient.get_instance()
