"""PDF 工具函数"""

from pathlib import Path
from typing import Literal

import img2pdf


def images_to_pdf(image_paths: list[str | Path], output_path: str | Path, page_size: str = "A4") -> bool:
    """
    将多个图片合成为一个 PDF 文件

    Args:
        image_paths: 图片文件路径列表
        output_path: 输出 PDF 文件路径
        page_size: 页面大小，默认 A4

    Returns:
        bool: 成功返回 True，失败返回 False

    Raises:
        FileNotFoundError: 图片文件不存在
        ValueError: 图片格式不支持
        Exception: 其他转换错误
    """
    try:
        # 验证输入图片文件
        valid_images = []
        for img_path in image_paths:
            img_path = Path(img_path)
            if not img_path.exists():
                raise FileNotFoundError(f"图片文件不存在: {img_path}")

            # 检查文件格式
            if img_path.suffix.lower() not in [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif"]:
                raise ValueError(f"不支持的图片格式: {img_path.suffix}")

            valid_images.append(str(img_path))

        if not valid_images:
            raise ValueError("没有有效的图片文件")

        # 确保输出目录存在
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 设置页面大小为 A4
        if page_size == "A4":
            a4inpt = (img2pdf.mm_to_pt(210), img2pdf.mm_to_pt(297))
            layout_fun = img2pdf.get_layout_fun(a4inpt)
        else:
            layout_fun = None

        # 转换图片为 PDF
        with open(output_path, "wb") as f:
            if layout_fun:
                f.write(img2pdf.convert(valid_images, layout_fun=layout_fun))
            else:
                f.write(img2pdf.convert(valid_images))

        return True

    except Exception as e:
        raise Exception(f"图片转 PDF 失败: {e!s}")


def directory_to_pdf(
    directory_path: str | Path,
    output_path: str | Path,
    sort_order: Literal["asc", "desc", "name_asc", "name_desc", "modified_asc", "modified_desc"] = "name_asc",
    page_size: str = "A4",
) -> bool:
    """
    将目录中的图片按指定顺序合成为 PDF

    Args:
        directory_path: 图片目录路径
        output_path: 输出 PDF 文件路径
        sort_order: 排序方式
            - "asc": 按创建时间正序（最老的在前）
            - "desc": 按创建时间倒序（最新的在前）
            - "name_asc": 按文件名正序（A-Z）
            - "name_desc": 按文件名倒序（Z-A）
            - "modified_asc": 按修改时间正序（最老的在前）
            - "modified_desc": 按修改时间倒序（最新的在前）
        page_size: 页面大小，默认 A4

    Returns:
        bool: 成功返回 True，失败返回 False

    Raises:
        FileNotFoundError: 目录不存在
        ValueError: 目录中没有图片文件
        Exception: 其他转换错误
    """
    try:
        directory_path = Path(directory_path)
        if not directory_path.exists():
            raise FileNotFoundError(f"目录不存在: {directory_path}")

        if not directory_path.is_dir():
            raise ValueError(f"路径不是目录: {directory_path}")

        # 支持的图片格式
        image_extensions = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif"}

        # 收集图片文件（仅当前目录）
        image_files = [f for f in directory_path.iterdir() if f.is_file() and f.suffix.lower() in image_extensions]

        if not image_files:
            raise ValueError(f"目录中没有找到图片文件: {directory_path}")

        # 按指定方式排序
        if sort_order == "asc":
            # 按创建时间正序
            image_files.sort(key=lambda x: x.stat().st_ctime)
        elif sort_order == "desc":
            # 按创建时间倒序
            image_files.sort(key=lambda x: x.stat().st_ctime, reverse=True)
        elif sort_order == "name_asc":
            # 按文件名正序
            image_files.sort(key=lambda x: x.name.lower())
        elif sort_order == "name_desc":
            # 按文件名倒序
            image_files.sort(key=lambda x: x.name.lower(), reverse=True)
        elif sort_order == "modified_asc":
            # 按修改时间正序
            image_files.sort(key=lambda x: x.stat().st_mtime)
        elif sort_order == "modified_desc":
            # 按修改时间倒序
            image_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        else:
            raise ValueError(f"不支持的排序方式: {sort_order}")

        # 转换为字符串路径列表
        image_paths = [str(f) for f in image_files]

        # 调用基础转换函数
        return images_to_pdf(image_paths, output_path, page_size)

    except Exception as e:
        raise Exception(f"目录转 PDF 失败: {e!s}")
