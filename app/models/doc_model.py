"""
文档模型 - 知识库文档相关数据库模型
"""

from datetime import datetime
import enum
import json
from typing import List, Optional

from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    Enum,
    Integer,
    String,
    Text,
    Index,
)
from sqlalchemy.orm import relationship

from .base import Base


class DocStatus(enum.Enum):
    """文档状态枚举"""
    
    COMPLETED = "0"      # 处理完成
    FAILED = "1"         # 处理失败
    DELETING = "5"       # 删除中
    PROCESSING = "2"     # 处理中（其他状态）


class DocModel(Base):
    """文档模型表"""

    __tablename__ = "docs"

    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    # 基本信息
    collection_name = Column(String(255), nullable=False, comment="索引所属的collection名")
    doc_name = Column(String(255), nullable=False, comment="文档名字")
    doc_id = Column(String(255), unique=True, nullable=False, comment="文档id")
    doc_type = Column(String(100), nullable=True, comment="文档类型")
    
    # 时间信息
    create_time = Column(String(50), nullable=True, comment="创建时间")
    update_time = Column(String(50), nullable=True, comment="文档更新时间")
    
    # 用户信息
    added_by = Column(String(255), nullable=True, comment="文档添加人")
    
    # 路径信息
    url = Column(String(500), nullable=True, comment="文档上传url路径")
    tos_path = Column(String(500), nullable=True, comment="文档上传tos路径")
    
    # 统计信息
    point_num = Column(Integer, default=0, comment="文档下知识点数量")
    
    # 状态信息
    status = Column(Enum(DocStatus), default=DocStatus.PROCESSING, nullable=False, comment="文档状态")
    
    # 内容信息
    title = Column(String(500), nullable=True, comment="文档标题")
    source = Column(String(255), nullable=True, comment="文档上传来源")
    
    # 字段列表（JSON格式存储）
    fields = Column(Text, nullable=True, comment="字段列表(JSON格式)")
    
    # 项目信息
    project = Column(String(255), default="default", comment="项目名称")
    resource_id = Column(String(255), nullable=True, comment="资源ID")
    
    # 系统时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="系统创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="系统更新时间")

    # 索引
    __table_args__ = (
        Index("idx_collection_name", "collection_name"),
        Index("idx_doc_id", "doc_id"),
        Index("idx_status", "status"),
        Index("idx_created_at", "created_at"),
        Index("idx_project", "project"),
        Index("idx_added_by", "added_by"),
    )

    def __repr__(self):
        return f"<DocModel(id={self.id}, doc_id='{self.doc_id}', doc_name='{self.doc_name}', status='{self.status.value}')>"

    @property
    def is_completed(self) -> bool:
        """是否处理完成"""
        return self.status == DocStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """是否处理失败"""
        return self.status == DocStatus.FAILED

    @property
    def is_processing(self) -> bool:
        """是否正在处理中"""
        return self.status == DocStatus.PROCESSING

    @property
    def is_deleting(self) -> bool:
        """是否正在删除中"""
        return self.status == DocStatus.DELETING

    def get_fields_list(self) -> List[dict]:
        """获取字段列表"""
        if not self.fields:
            return []
        try:
            return json.loads(self.fields)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_fields_list(self, fields_list: List[dict]):
        """设置字段列表"""
        if fields_list:
            self.fields = json.dumps(fields_list, ensure_ascii=False)
        else:
            self.fields = None

    def get_status_description(self) -> str:
        """获取状态描述"""
        status_map = {
            DocStatus.COMPLETED: "处理完成",
            DocStatus.FAILED: "处理失败", 
            DocStatus.DELETING: "删除中",
            DocStatus.PROCESSING: "处理中"
        }
        return status_map.get(self.status, "未知状态")

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": self.id,
            "collection_name": self.collection_name,
            "doc_name": self.doc_name,
            "doc_id": self.doc_id,
            "doc_type": self.doc_type,
            "create_time": self.create_time,
            "added_by": self.added_by,
            "update_time": self.update_time,
            "url": self.url,
            "tos_path": self.tos_path,
            "point_num": self.point_num,
            "status": self.status.value,
            "title": self.title,
            "source": self.source,
            "fields": self.get_fields_list(),
            "project": self.project,
            "resource_id": self.resource_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_volcengine_doc(cls, doc_data: dict) -> 'DocModel':
        """从火山引擎文档数据创建DocModel实例"""
        return cls(
            collection_name=doc_data.get("collection_name"),
            doc_name=doc_data.get("doc_name"),
            doc_id=doc_data.get("doc_id"),
            doc_type=doc_data.get("doc_type"),
            create_time=doc_data.get("create_time"),
            added_by=doc_data.get("added_by"),
            update_time=doc_data.get("update_time"),
            url=doc_data.get("url"),
            tos_path=doc_data.get("tos_path"),
            point_num=doc_data.get("point_num", 0),
            status=DocStatus(doc_data.get("status", "2")),
            title=doc_data.get("title"),
            source=doc_data.get("source"),
            fields=json.dumps(doc_data.get("fields", []), ensure_ascii=False) if doc_data.get("fields") else None,
            project=doc_data.get("project", "default"),
            resource_id=doc_data.get("resource_id"),
        )
