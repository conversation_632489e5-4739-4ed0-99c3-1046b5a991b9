"""
文档模型 - 知识库文档返回值模型
"""

import json
from typing import List, Optional, Any
from dataclasses import dataclass


@dataclass
class DocModel:
    """文档返回值模型"""

    collection_name: Optional[str] = None      # 索引所属的collection名
    doc_name: Optional[str] = None             # 文档名字
    doc_id: Optional[str] = None               # 文档id
    doc_type: Optional[str] = None             # 文档类型
    create_time: Optional[str] = None          # 创建时间
    added_by: Optional[str] = None             # 文档添加人
    update_time: Optional[str] = None          # 文档更新时间
    url: Optional[str] = None                  # 文档上传url路径
    tos_path: Optional[str] = None             # 文档上传tos路径
    point_num: Optional[int] = None            # 文档下知识点数量
    status: Optional[str] = None               # 文档状态
    title: Optional[str] = None                # 文档标题
    source: Optional[str] = None               # 文档上传来源
    fields: Optional[List[Any]] = None         # 字段列表
    project: Optional[str] = "default"         # 项目名称
    resource_id: Optional[str] = None          # 资源ID

    def __init__(self, kwargs: dict):
        """从字典初始化DocModel"""
        self.collection_name = kwargs.get("collection_name")
        self.doc_name = kwargs.get("doc_name")
        self.doc_id = kwargs.get("doc_id")
        self.doc_type = kwargs.get("doc_type")
        self.create_time = kwargs.get("create_time")
        self.added_by = kwargs.get("added_by")
        self.update_time = kwargs.get("update_time")
        self.url = kwargs.get("url")
        self.tos_path = kwargs.get("tos_path")
        self.point_num = kwargs.get("point_num")
        self.status = kwargs.get("status")
        self.title = kwargs.get("title")
        self.source = kwargs.get("source")
        self.fields = []

        # 处理字段列表
        meta = kwargs.get("doc_meta") or kwargs.get("meta")
        if meta is not None:
            try:
                if isinstance(meta, str):
                    meta = json.loads(meta)
                self.fields = [Field(field) for field in meta] if meta else []
            except (json.JSONDecodeError, TypeError):
                self.fields = []

        self.project = kwargs.get("project", "default")
        self.resource_id = kwargs.get("resource_id")

    def get_status_description(self) -> str:
        """获取状态描述"""
        status_map = {
            "0": "处理完成",
            "1": "处理失败",
            "5": "删除中",
        }
        return status_map.get(self.status, "处理中")

    @property
    def is_completed(self) -> bool:
        """是否处理完成"""
        return self.status == "0"

    @property
    def is_failed(self) -> bool:
        """是否处理失败"""
        return self.status == "1"

    @property
    def is_deleting(self) -> bool:
        """是否正在删除中"""
        return self.status == "5"

    @property
    def is_processing(self) -> bool:
        """是否正在处理中"""
        return self.status not in ["0", "1", "5"]

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "collection_name": self.collection_name,
            "doc_name": self.doc_name,
            "doc_id": self.doc_id,
            "doc_type": self.doc_type,
            "create_time": self.create_time,
            "added_by": self.added_by,
            "update_time": self.update_time,
            "url": self.url,
            "tos_path": self.tos_path,
            "point_num": self.point_num,
            "status": self.status,
            "title": self.title,
            "source": self.source,
            "fields": [field.to_dict() if hasattr(field, 'to_dict') else field for field in (self.fields or [])],
            "project": self.project,
            "resource_id": self.resource_id,
        }

    def __repr__(self):
        return f"<DocModel(doc_id='{self.doc_id}', doc_name='{self.doc_name}', status='{self.status}')>"


@dataclass
class Field:
    """字段模型"""

    def __init__(self, field_data: dict):
        """从字典初始化Field"""
        self.field_data = field_data
        # 可以根据需要添加具体的字段属性
        for key, value in field_data.items():
            setattr(self, key, value)

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return self.field_data

    def __repr__(self):
        return f"<Field({self.field_data})>"
