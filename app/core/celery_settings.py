"""
Celery专用配置设置
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional
from app.core.config import settings


class CeleryWorkerConfig(BaseModel):
    """单个Worker配置"""
    name: str
    queues: str
    concurrency: int
    pool: str = 'prefork'
    max_tasks_per_child: int = 1000
    time_limit: int = 2400  # 40分钟
    soft_time_limit: int = 1800  # 30分钟


class CelerySettings(BaseModel):
    """Celery配置设置"""
    
    # Broker和Backend配置
    broker_url: str = Field(default_factory=lambda: getattr(settings, 'CELERY_BROKER_URL', settings.REDIS_URL))
    result_backend: str = Field(default_factory=lambda: getattr(settings, 'CELERY_RESULT_BACKEND', settings.REDIS_URL.replace('/0', '/1')))
    
    # 任务序列化配置
    task_serializer: str = 'json'
    accept_content: List[str] = ['json']
    result_serializer: str = 'json'
    
    # 时区设置
    timezone: str = 'UTC'
    enable_utc: bool = True
    
    # 任务超时设置
    task_soft_time_limit: int = Field(default_factory=lambda: getattr(settings, 'CELERY_TASK_SOFT_TIME_LIMIT', 1800))
    task_time_limit: int = Field(default_factory=lambda: getattr(settings, 'CELERY_TASK_TIME_LIMIT', 2400))
    
    # 重试和确认设置
    task_acks_late: bool = True
    worker_prefetch_multiplier: int = 1
    
    # 结果过期时间
    result_expires: int = 3600  # 1小时
    
    # 任务压缩
    task_compression: str = 'gzip'
    result_compression: str = 'gzip'
    
    # 工作进程设置
    worker_max_tasks_per_child: int = Field(default_factory=lambda: getattr(settings, 'CELERY_WORKER_MAX_TASKS_PER_CHILD', 1000))
    worker_disable_rate_limits: bool = False
    
    # 监控设置
    worker_send_task_events: bool = True
    task_send_sent_event: bool = True
    
    # 错误处理
    task_reject_on_worker_lost: bool = True
    task_ignore_result: bool = False
    
    # Worker配置
    workers: List[CeleryWorkerConfig] = Field(default_factory=lambda: [
        CeleryWorkerConfig(
            name='ocr_worker',
            queues='ocr_queue',
            concurrency=getattr(settings, 'OCR_WORKER_CONCURRENCY', 2)
        ),
        CeleryWorkerConfig(
            name='ai_worker',
            queues='ai_queue',
            concurrency=getattr(settings, 'AI_WORKER_CONCURRENCY', 4)
        ),
        CeleryWorkerConfig(
            name='post_worker',
            queues='post_queue',
            concurrency=getattr(settings, 'POST_WORKER_CONCURRENCY', 3)
        ),
        CeleryWorkerConfig(
            name='api_worker',
            queues='api_queue',
            concurrency=getattr(settings, 'API_WORKER_CONCURRENCY', 2)
        ),
        CeleryWorkerConfig(
            name='workflow_worker',
            queues='workflow_queue',
            concurrency=2
        )
    ])
    
    # 任务路由配置
    task_routes: Dict[str, Dict[str, str]] = {
        'app.tasks.ocr_tasks.*': {'queue': 'ocr_queue'},
        'app.tasks.ai_tasks.*': {'queue': 'ai_queue'},
        'app.tasks.post_process_tasks.*': {'queue': 'post_queue'},
        'app.tasks.storage_tasks.*': {'queue': 'api_queue'},
        'app.tasks.workflow_tasks.*': {'queue': 'workflow_queue'},
    }
    
    # Beat调度器配置
    beat_schedule: Dict = {}
    
    @validator('broker_url', 'result_backend')
    def validate_redis_url(cls, v):
        """验证Redis URL格式"""
        if not v.startswith(('redis://', 'rediss://')):
            raise ValueError('Redis URL must start with redis:// or rediss://')
        return v
    
    @validator('workers')
    def validate_workers(cls, v):
        """验证Worker配置"""
        if not v:
            raise ValueError('At least one worker must be configured')
        
        names = [worker.name for worker in v]
        if len(names) != len(set(names)):
            raise ValueError('Worker names must be unique')
        
        return v
    
    def get_celery_config(self) -> Dict:
        """获取Celery配置字典"""
        from kombu import Queue
        
        config = {
            'broker_url': self.broker_url,
            'result_backend': self.result_backend,
            'task_serializer': self.task_serializer,
            'accept_content': self.accept_content,
            'result_serializer': self.result_serializer,
            'timezone': self.timezone,
            'enable_utc': self.enable_utc,
            'task_routes': self.task_routes,
            'task_queues': tuple([
                Queue(worker.queues, routing_key=worker.queues.replace('_queue', ''))
                for worker in self.workers
            ]),
            'task_soft_time_limit': self.task_soft_time_limit,
            'task_time_limit': self.task_time_limit,
            'task_acks_late': self.task_acks_late,
            'worker_prefetch_multiplier': self.worker_prefetch_multiplier,
            'result_expires': self.result_expires,
            'task_compression': self.task_compression,
            'result_compression': self.result_compression,
            'worker_max_tasks_per_child': self.worker_max_tasks_per_child,
            'worker_disable_rate_limits': self.worker_disable_rate_limits,
            'worker_send_task_events': self.worker_send_task_events,
            'task_send_sent_event': self.task_send_sent_event,
            'task_reject_on_worker_lost': self.task_reject_on_worker_lost,
            'task_ignore_result': self.task_ignore_result,
            'beat_schedule': self.beat_schedule,
        }
        
        return config


class TaskPollerSettings(BaseModel):
    """任务轮询器配置"""
    
    polling_interval: int = Field(default_factory=lambda: getattr(settings, 'TASK_POLLING_INTERVAL', 5))
    batch_size: int = Field(default_factory=lambda: getattr(settings, 'TASK_POLLING_BATCH_SIZE', 10))
    max_retries: int = 3
    retry_delay: int = 10  # 秒
    
    @validator('polling_interval')
    def validate_polling_interval(cls, v):
        if v < 1:
            raise ValueError('Polling interval must be at least 1 second')
        return v
    
    @validator('batch_size')
    def validate_batch_size(cls, v):
        if v < 1 or v > 100:
            raise ValueError('Batch size must be between 1 and 100')
        return v


class WorkflowSettings(BaseModel):
    """工作流配置"""
    
    # 任务超时配置
    ocr_timeout: int = Field(default_factory=lambda: getattr(settings, 'OCR_TIMEOUT_SECONDS', 300))
    ai_timeout: int = Field(default_factory=lambda: getattr(settings, 'AGENT_TIMEOUT_SECONDS', 180))
    api_timeout: int = 30
    storage_timeout: int = 60
    
    # 重试配置
    max_retries: int = 3
    retry_backoff: bool = True
    retry_backoff_max: int = 600  # 10分钟
    retry_jitter: bool = True
    
    # 缓存配置
    ocr_cache_size: int = Field(default_factory=lambda: getattr(settings, 'OCR_CACHE_SIZE', 100))
    ocr_cache_expire: int = Field(default_factory=lambda: getattr(settings, 'OCR_CACHE_EXPIRE_MINUTES', 60))
    
    # 文件存储配置
    temp_file_cleanup: bool = True
    result_file_retention_days: int = 30
    
    @validator('ocr_timeout', 'ai_timeout', 'api_timeout', 'storage_timeout')
    def validate_timeout(cls, v):
        if v < 10:
            raise ValueError('Timeout must be at least 10 seconds')
        return v


# 全局配置实例
celery_settings = CelerySettings()
task_poller_settings = TaskPollerSettings()
workflow_settings = WorkflowSettings()