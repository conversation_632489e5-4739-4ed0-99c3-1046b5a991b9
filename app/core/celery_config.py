"""
Celery配置模块
"""

from celery import Celery
from app.core.celery_settings import celery_settings

# 创建Celery应用实例
celery_app = Celery('health_report_workflow')

# 使用配置类更新Celery配置
celery_app.conf.update(celery_settings.get_celery_config())

# 自动发现任务模块
celery_app.autodiscover_tasks([
    'app.tasks.ocr_tasks',
    'app.tasks.ai_tasks', 
    'app.tasks.post_process_tasks',
    'app.tasks.storage_tasks',
    'app.tasks.workflow_tasks',
])