"""
Celery Flower监控配置
"""

import os
import subprocess
import threading
import time
import logging
from typing import Optional
from app.core.config import settings
from app.core.celery_settings import celery_settings

logger = logging.getLogger(__name__)


class FlowerManager:
    """Flower监控管理器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.running = False
        self.port = getattr(settings, 'FLOWER_PORT', 5555)
        self.address = getattr(settings, 'FLOWER_ADDRESS', '0.0.0.0')
        self.url_prefix = getattr(settings, 'FLOWER_URL_PREFIX', '')
        self.basic_auth = getattr(settings, 'FLOWER_BASIC_AUTH', None)
        self.broker_api = getattr(settings, 'FLOWER_BROKER_API', None)
        
    def start_flower(self):
        """启动Flower监控服务"""
        if self.running:
            logger.warning("Flower is already running")
            return
        
        try:
            cmd = [
                'celery',
                '-A', 'app.core.celery_config:celery_app',
                'flower',
                f'--port={self.port}',
                f'--address={self.address}',
                '--loglevel=info'
            ]
            
            # 添加URL前缀（如果配置了）
            if self.url_prefix:
                cmd.extend([f'--url_prefix={self.url_prefix}'])
            
            # 添加基础认证（如果配置了）
            if self.basic_auth:
                cmd.extend([f'--basic_auth={self.basic_auth}'])
            
            # 添加Broker API（如果配置了）
            if self.broker_api:
                cmd.extend([f'--broker_api={self.broker_api}'])
            
            # 设置环境变量
            env = os.environ.copy()
            env.update({
                'CELERY_BROKER_URL': celery_settings.broker_url,
                'CELERY_RESULT_BACKEND': celery_settings.result_backend,
            })
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            
            self.running = True
            
            logger.info(f"Flower started on {self.address}:{self.port} with PID {self.process.pid}")
            
            # 启动监控线程
            monitor_thread = threading.Thread(target=self._monitor_flower, daemon=True)
            monitor_thread.start()
            
        except Exception as e:
            logger.error(f"Failed to start Flower: {e}")
            raise
    
    def stop_flower(self):
        """停止Flower监控服务"""
        if not self.running or not self.process:
            return
        
        try:
            self.process.terminate()
            self.process.wait(timeout=10)
            logger.info("Flower stopped gracefully")
        except subprocess.TimeoutExpired:
            self.process.kill()
            self.process.wait()
            logger.warning("Flower was forcefully killed")
        except Exception as e:
            logger.error(f"Error stopping Flower: {e}")
        finally:
            self.process = None
            self.running = False
    
    def restart_flower(self):
        """重启Flower监控服务"""
        logger.info("Restarting Flower...")
        self.stop_flower()
        time.sleep(2)
        self.start_flower()
    
    def is_running(self) -> bool:
        """检查Flower是否正在运行"""
        return self.running and self.process is not None and self.process.poll() is None
    
    def get_status(self) -> dict:
        """获取Flower状态信息"""
        return {
            'running': self.is_running(),
            'pid': self.process.pid if self.process else None,
            'port': self.port,
            'address': self.address,
            'url': f"http://{self.address}:{self.port}{self.url_prefix}",
            'basic_auth_enabled': bool(self.basic_auth)
        }
    
    def _monitor_flower(self):
        """监控Flower进程"""
        while self.running:
            try:
                if self.process and self.process.poll() is not None:
                    logger.warning("Flower process died, restarting...")
                    self.restart_flower()
                    break
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"Error in Flower monitoring: {e}")
                time.sleep(10)


class FlowerConfig:
    """Flower配置类"""
    
    @staticmethod
    def get_flower_settings() -> dict:
        """获取Flower配置设置"""
        return {
            'port': getattr(settings, 'FLOWER_PORT', 5555),
            'address': getattr(settings, 'FLOWER_ADDRESS', '0.0.0.0'),
            'url_prefix': getattr(settings, 'FLOWER_URL_PREFIX', ''),
            'basic_auth': getattr(settings, 'FLOWER_BASIC_AUTH', None),
            'broker_api': getattr(settings, 'FLOWER_BROKER_API', None),
            'persistent': getattr(settings, 'FLOWER_PERSISTENT', True),
            'db': getattr(settings, 'FLOWER_DB', 'flower.db'),
            'max_tasks': getattr(settings, 'FLOWER_MAX_TASKS', 10000),
            'auto_refresh': getattr(settings, 'FLOWER_AUTO_REFRESH', True),
            'enable_events': getattr(settings, 'FLOWER_ENABLE_EVENTS', True),
        }
    
    @staticmethod
    def create_flower_config_file(config_path: str = 'flowerconfig.py'):
        """创建Flower配置文件"""
        config_content = f'''
# Flower配置文件
import os

# Celery配置
broker_url = "{celery_settings.broker_url}"
result_backend = "{celery_settings.result_backend}"

# Flower特定配置
port = {getattr(settings, 'FLOWER_PORT', 5555)}
address = "{getattr(settings, 'FLOWER_ADDRESS', '0.0.0.0')}"
url_prefix = "{getattr(settings, 'FLOWER_URL_PREFIX', '')}"

# 认证配置
basic_auth = {repr(getattr(settings, 'FLOWER_BASIC_AUTH', None))}

# 数据库配置
persistent = {getattr(settings, 'FLOWER_PERSISTENT', True)}
db = "{getattr(settings, 'FLOWER_DB', 'flower.db')}"

# 任务配置
max_tasks = {getattr(settings, 'FLOWER_MAX_TASKS', 10000)}
auto_refresh = {getattr(settings, 'FLOWER_AUTO_REFRESH', True)}
enable_events = {getattr(settings, 'FLOWER_ENABLE_EVENTS', True)}

# 日志配置
logging = 'INFO'

# 自定义页面配置
natural_time = True
tasks_columns = "name,uuid,state,args,kwargs,result,received,started,runtime,worker"
'''
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            logger.info(f"Flower config file created: {config_path}")
        except Exception as e:
            logger.error(f"Failed to create Flower config file: {e}")


# 全局Flower管理器实例
_flower_manager: Optional[FlowerManager] = None
_flower_manager_lock = threading.Lock()


def get_flower_manager() -> FlowerManager:
    """获取Flower管理器实例（单例模式）"""
    global _flower_manager
    if _flower_manager is None:
        with _flower_manager_lock:
            if _flower_manager is None:
                _flower_manager = FlowerManager()
    return _flower_manager