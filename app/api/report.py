"""
体检报告相关 API 路由
"""

import json
import os
import tempfile
from typing import Any

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from app.agents.report_agent import AgentType, ReportAnalysisWorkflow
from app.schemas.pdf import PDFParseRequest, PDFParseResponse
from app.schemas.report_task import (
    ReportParseRequest,
    ReportParseResponse,
    TaskListResponse,
    TaskRestartRequest,
    TaskRestartResponse,
    TaskStatusResponse,
)
from app.schemas.response import ApiResponse
from app.services.report_ocr_handler import ReportOCRHandler, get_report_ocr_handler
from app.services.report_task_manager import get_task_manager

router = APIRouter(tags=["report"])


class ReportAnalysisRequest(BaseModel):
    """报告分析请求"""

    report_text: str = Field(..., description="体检报告文本")
    user_id: str | None = Field(None, description="用户ID")
    session_id: str | None = Field(None, description="会话ID")


class SingleAgentRequest(BaseModel):
    """单个Agent执行请求"""

    input_text: str = Field(..., description="输入文本")
    agent_type: AgentType = Field(..., description="Agent类型")
    user_id: str | None = Field(None, description="用户ID")
    session_id: str | None = Field(None, description="会话ID")


class ReportAnalysisResponse(BaseModel):
    """报告分析响应"""

    data: dict[str, Any] | None = None
    processing_summary: dict[str, Any] | None = None


# PDF 解析相关端点
@router.post("/parse-file", response_model=ApiResponse[PDFParseResponse])
async def parse_pdf_file(
    file: UploadFile = File(..., description="PDF 文件"),
    page_start: int = 0,
    page_num: int = 20,
    pdf_handler: ReportOCRHandler = Depends(get_report_ocr_handler),
) -> JSONResponse:
    """上传并解析 PDF 文件"""

    if not file.filename or not file.filename.lower().endswith(".pdf"):
        raise HTTPException(status_code=400, detail="只支持 PDF 文件")

    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
    content = await file.read()
    temp_file.write(content)
    temp_file.close()

    result = await pdf_handler.parse_pdf_file(temp_file.name, page_start=page_start, page_num=page_num)

    if temp_file and os.path.exists(temp_file.name):
        os.unlink(temp_file.name)

    return JSONResponse(status_code=200, content={"code": 0, "message": "success", "data": result})


@router.post("/parse-url", response_model=ApiResponse[PDFParseResponse])
async def parse_pdf_url(
    request: PDFParseRequest, pdf_handler: ReportOCRHandler = Depends(get_report_ocr_handler)
) -> JSONResponse:
    """通过 URL 解析 PDF 文件"""

    if not request.file_url:
        raise HTTPException(status_code=400, detail="file_url 不能为空")

    result = await pdf_handler.parse_pdf_url(request.file_url, page_start=request.page_start, page_num=request.page_num)

    return JSONResponse(status_code=200, content={"code": 0, "message": "success", "data": result})


# 体检报告分析相关端点
@router.post("/analyze", response_model=ReportAnalysisResponse)
async def analyze_report(request: ReportAnalysisRequest) -> ReportAnalysisResponse:
    """
    分析体检报告（同步返回最终结果）

    执行所有Agent的完整分析流程，等待全部完成后一次性返回结果：
    1. 个人信息和机构信息提取
    2. 检查项目解析
    3. 专业报告解析
    4. 综合分析
    """

    workflow = ReportAnalysisWorkflow(user_id=request.user_id, session_id=request.session_id)

    final_result = None
    events_count = 0

    for event in workflow.run(request.report_text):
        events_count += 1
        if event.event == "run_response_completed":
            final_result = json.loads(event.content.split("最终分析结果: \n")[1])

    if final_result is None:
        raise HTTPException(status_code=500, detail="未能获取到分析结果")

    return ReportAnalysisResponse(
        data=final_result,
        processing_summary={
            "steps_completed": 4,
            "total_steps": 4,
            "completion_rate": "100%",
            "events_processed": events_count,
        },
    )


@router.post("/agent/run")
async def run_single_agent(request: SingleAgentRequest) -> ApiResponse:
    """
    执行单个Agent（同步返回最终结果）

    支持的Agent类型：
    - meta_extractor: 个人信息和机构信息提取
    - tabular_parser: 检查项目解析
    - ....
    """

    workflow = ReportAnalysisWorkflow(user_id=request.user_id, session_id=request.session_id, debug_mode=True)
    rs = workflow.run_single_agent(agent_type=request.agent_type, input_text=request.input_text)

    return ApiResponse.success(rs.content)


# 新的异步报告解析任务相关端点


@router.post("/parse", response_model=ApiResponse[ReportParseResponse])
async def submit_report_parse_task(
    request: ReportParseRequest, task_manager=Depends(get_task_manager)
) -> ApiResponse[ReportParseResponse]:
    """
    提交报告解析任务

    接收reportId和reportKey，创建异步解析任务：
    1. 写入任务表（状态为PENDING）
    2. 任务轮询器会自动发现并提交到Celery工作流
    3. 返回任务ID和状态
    """
    try:
        # 使用现有的任务管理器提交任务（保持向后兼容）
        # 新的Celery系统会通过轮询器自动处理PENDING状态的任务
        result = await task_manager.submit_task(report_id=request.report_id, report_key=request.report_key)

        response_data = ReportParseResponse(
            task_id=result["task_id"], status=result["status"], message=result["message"]
        )

        return ApiResponse.success(response_data, "任务提交成功，将通过Celery工作流处理")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交任务失败: {e!s}")


@router.post("/parse-celery", response_model=ApiResponse[ReportParseResponse])
async def submit_report_parse_task_direct(request: ReportParseRequest) -> ApiResponse[ReportParseResponse]:
    """
    直接提交报告解析任务到Celery工作流（新接口）

    接收reportId和reportKey，直接创建Celery工作流任务：
    1. 写入任务表
    2. 直接提交到Celery工作流
    3. 返回任务ID和工作流ID
    """
    try:
        from app.models import get_db, ReportTask, TaskStatus
        from app.tasks.workflow_tasks import create_report_workflow
        from datetime import datetime
        
        # 创建任务记录
        db = next(get_db())
        try:
            # 检查任务是否已存在
            existing_task = db.query(ReportTask).filter(ReportTask.report_id == request.report_id).first()
            
            if existing_task:
                if existing_task.status in [TaskStatus.PENDING, TaskStatus.FAILED]:
                    # 重新提交失败或待处理的任务
                    existing_task.status = TaskStatus.PENDING
                    existing_task.retry_count = 0
                    existing_task.error_message = None
                    existing_task.updated_at = datetime.utcnow()
                    db.commit()
                    
                    # 直接提交到Celery
                    workflow_result = create_report_workflow.delay(
                        report_id=request.report_id,
                        report_key=request.report_key
                    )
                    
                    # 保存工作流ID
                    existing_task.workflow_id = workflow_result.id
                    db.commit()
                    
                    response_data = ReportParseResponse(
                        task_id=request.report_id,
                        status="resubmitted",
                        message=f"任务已重新提交到Celery工作流，workflow_id: {workflow_result.id}"
                    )
                    
                    return ApiResponse.success(response_data, "任务重新提交成功")
                else:
                    response_data = ReportParseResponse(
                        task_id=request.report_id,
                        status=existing_task.status.value,
                        message="任务已存在且正在处理中"
                    )
                    return ApiResponse.success(response_data, "任务已存在")
            
            # 创建新任务
            new_task = ReportTask(
                report_id=request.report_id,
                report_key=request.report_key,
                status=TaskStatus.PENDING
            )
            
            db.add(new_task)
            db.commit()
            
            # 直接提交到Celery工作流
            workflow_result = create_report_workflow.delay(
                report_id=request.report_id,
                report_key=request.report_key
            )
            
            # 保存工作流ID
            new_task.workflow_id = workflow_result.id
            db.commit()
            
            response_data = ReportParseResponse(
                task_id=request.report_id,
                status="submitted",
                message=f"任务已提交到Celery工作流，workflow_id: {workflow_result.id}"
            )
            
            return ApiResponse.success(response_data, "任务提交成功")
            
        finally:
            db.close()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"提交Celery任务失败: {e!s}")


@router.get("/task/{report_id}/status", response_model=ApiResponse[TaskStatusResponse])
async def get_task_status(report_id: str, task_manager=Depends(get_task_manager)) -> ApiResponse[TaskStatusResponse]:
    """
    查询任务状态

    返回详细的任务处理状态和进度信息
    """
    try:
        status = await task_manager.get_task_status(report_id)

        if not status:
            raise HTTPException(status_code=404, detail="任务不存在")

        return ApiResponse.success(status, "获取任务状态成功")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {e!s}")


@router.get("/task/{report_id}/status-enhanced")
async def get_enhanced_task_status(report_id: str) -> ApiResponse:
    """
    查询增强的任务状态（包含Celery工作流信息）

    返回详细的任务处理状态、工作流信息和步骤详情
    """
    try:
        from app.models import get_db, ReportTask, TaskStep, TaskResult
        
        db = next(get_db())
        try:
            # 获取主任务信息
            task = db.query(ReportTask).filter(ReportTask.report_id == report_id).first()
            
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            # 获取任务步骤信息
            steps = db.query(TaskStep).filter(TaskStep.report_id == report_id).all()
            
            # 获取任务结果信息
            results = db.query(TaskResult).filter(TaskResult.report_id == report_id).all()
            
            # 构建响应数据
            enhanced_status = {
                "report_id": task.report_id,
                "report_key": task.report_key,
                "status": task.status.value,
                "workflow_id": task.workflow_id,
                "progress_percent": float(task.progress_percent) if task.progress_percent else 0.0,
                "retry_count": task.retry_count,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "steps": [
                    {
                        "step_name": step.step_name,
                        "status": step.status,
                        "celery_task_id": step.celery_task_id,
                        "start_time": step.start_time.isoformat() if step.start_time else None,
                        "end_time": step.end_time.isoformat() if step.end_time else None,
                        "duration": step.duration,
                        "error_message": step.error_message
                    }
                    for step in steps
                ],
                "results": [
                    {
                        "result_type": result.result_type,
                        "file_path": result.file_path,
                        "created_at": result.created_at.isoformat() if result.created_at else None
                    }
                    for result in results
                ],
                "total_steps": len(steps),
                "completed_steps": len([s for s in steps if s.status == 'completed']),
                "failed_steps": len([s for s in steps if s.status == 'failed'])
            }
            
            return ApiResponse.success(enhanced_status, "获取增强任务状态成功")
            
        finally:
            db.close()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取增强任务状态失败: {e!s}")


@router.post("/task/{report_id}/restart", response_model=ApiResponse[TaskRestartResponse])
async def restart_task(
    report_id: str, request: TaskRestartRequest, task_manager=Depends(get_task_manager)
) -> ApiResponse[TaskRestartResponse]:
    """
    重新执行任务

    支持从指定步骤重新开始执行，或从头开始
    """
    try:
        result = await task_manager.restart_task(report_id=report_id, from_step=request.from_step or 0)

        response_data = TaskRestartResponse(
            report_id=result["report_id"],
            status=result["status"],
            message=result["message"],
            restarted_from_step=result.get("restarted_from_step", 0),
        )

        return ApiResponse.success(response_data, "任务重启成功")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重启任务失败: {e!s}")



@router.get("/tasks", response_model=ApiResponse[TaskListResponse])
async def get_task_list(
    status: str | None = None,
    page: int = 1,
    size: int = 20,
    sort_by: str = "created_at",
    sort_order: str = "desc",
    task_manager=Depends(get_task_manager),
) -> ApiResponse[TaskListResponse]:
    """
    获取任务列表

    支持分页和按状态筛选
    """
    try:
        from app.services.task_status_service import get_task_status_service
        
        status_service = get_task_status_service()
        result = status_service.get_task_list(
            status=status,
            page=page,
            size=size,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # 转换为响应格式
        response_data = TaskListResponse(
            tasks=result["tasks"],
            total=result["total"],
            page=result["page"],
            size=result["size"],
            pages=result["pages"]
        )

        return ApiResponse.success(response_data, "获取任务列表成功")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {e!s}")


@router.get("/tasks/statistics")
async def get_task_statistics() -> ApiResponse:
    """
    获取任务统计信息

    返回任务的各种统计数据
    """
    try:
        from app.services.task_status_service import get_task_status_service
        
        status_service = get_task_status_service()
        stats = status_service.get_task_statistics()
        
        return ApiResponse.success(stats, "获取任务统计成功")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取任务统计失败: {e!s}")


@router.get("/task/{report_id}/detailed-status")
async def get_detailed_task_status(report_id: str) -> ApiResponse:
    """
    获取任务的详细状态信息

    包含步骤详情、结果摘要、错误历史等完整信息
    """
    try:
        from app.services.task_status_service import get_task_status_service
        
        status_service = get_task_status_service()
        detailed_status = status_service.get_task_status(report_id)
        
        if not detailed_status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return ApiResponse.success(detailed_status, "获取详细任务状态成功")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取详细任务状态失败: {e!s}")


@router.post("/task/{report_id}/cancel")
async def cancel_task_workflow(report_id: str, reason: str = "User requested") -> ApiResponse:
    """
    取消任务工作流

    Args:
        report_id: 报告ID
        reason: 取消原因

    Returns:
        dict: 取消结果
    """
    try:
        from app.tasks.workflow_tasks import cancel_workflow
        
        result = cancel_workflow.delay(report_id=report_id, reason=reason)
        
        return ApiResponse.success({
            "report_id": report_id,
            "status": "cancellation_requested",
            "reason": reason,
            "task_id": result.id
        }, "工作流取消请求已提交")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消工作流失败: {e!s}")


@router.post("/task/{report_id}/restart-from-step")
async def restart_task_from_step(
    report_id: str, 
    from_step: str, 
    cleanup_partial: bool = True
) -> ApiResponse:
    """
    从指定步骤重启任务工作流

    Args:
        report_id: 报告ID
        from_step: 重启的步骤名称 (ocr, ai_analysis, post_processing, storage)
        cleanup_partial: 是否清理部分结果

    Returns:
        dict: 重启结果
    """
    try:
        from app.tasks.workflow_tasks import restart_workflow_from_step
        
        valid_steps = ['ocr', 'ai_analysis', 'post_processing', 'storage']
        if from_step not in valid_steps:
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid step. Must be one of: {', '.join(valid_steps)}"
            )
        
        result = restart_workflow_from_step.delay(
            report_id=report_id,
            from_step=from_step,
            cleanup_partial=cleanup_partial
        )
        
        return ApiResponse.success({
            "report_id": report_id,
            "status": "restart_requested",
            "from_step": from_step,
            "cleanup_partial": cleanup_partial,
            "task_id": result.id
        }, f"工作流重启请求已提交，从步骤 {from_step} 开始")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重启工作流失败: {e!s}")


@router.post("/task/{report_id}/compensate")
async def trigger_compensation(report_id: str, failed_step: str) -> ApiResponse:
    """
    手动触发补偿操作

    Args:
        report_id: 报告ID
        failed_step: 失败的步骤名称

    Returns:
        dict: 补偿结果
    """
    try:
        from app.tasks.workflow_compensation import execute_compensation_actions
        
        result = execute_compensation_actions.delay(
            report_id=report_id,
            failed_step=failed_step,
            error_details={"manual_trigger": True}
        )
        
        return ApiResponse.success({
            "report_id": report_id,
            "failed_step": failed_step,
            "status": "compensation_requested",
            "task_id": result.id
        }, "补偿操作请求已提交")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"触发补偿操作失败: {e!s}")
