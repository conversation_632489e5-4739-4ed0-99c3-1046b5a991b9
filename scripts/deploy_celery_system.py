#!/usr/bin/env python3
"""
Celery工作流系统部署脚本
"""

import argparse
import logging
import os
import subprocess
import sys
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CelerySystemDeployer:
    """Celery系统部署器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.scripts_dir = self.project_root / "scripts"
        self.config_dir = self.project_root / "config"
        
    def check_dependencies(self):
        """检查系统依赖"""
        logger.info("检查系统依赖...")
        
        required_packages = [
            'celery',
            'redis',
            'flower',
            'pymysql',
            'sqlalchemy',
            'fastapi',
            'uvicorn'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"✓ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"✗ {package} 未安装")
        
        if missing_packages:
            logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
            logger.info("请运行: pip install " + " ".join(missing_packages))
            return False
        
        return True
    
    def check_services(self):
        """检查外部服务"""
        logger.info("检查外部服务...")
        
        # 检查Redis
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            r.ping()
            logger.info("✓ Redis 服务正常")
        except Exception as e:
            logger.error(f"✗ Redis 服务异常: {e}")
            return False
        
        # 检查MySQL
        try:
            import pymysql
            from app.core.config import settings
            
            connection = pymysql.connect(
                host=settings.MYSQL_HOST,
                port=settings.MYSQL_PORT,
                user=settings.MYSQL_USER,
                password=settings.MYSQL_PASSWORD,
                database=settings.MYSQL_DATABASE
            )
            connection.close()
            logger.info("✓ MySQL 服务正常")
        except Exception as e:
            logger.error(f"✗ MySQL 服务异常: {e}")
            return False
        
        return True
    
    def run_migrations(self):
        """运行数据库迁移"""
        logger.info("运行数据库迁移...")
        
        try:
            # 导入迁移运行器
            sys.path.append(str(self.project_root))
            from app.migrations.migration_runner import MigrationRunner
            
            runner = MigrationRunner()
            runner.migrate()
            logger.info("✓ 数据库迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"✗ 数据库迁移失败: {e}")
            return False
    
    def create_systemd_services(self):
        """创建systemd服务文件"""
        logger.info("创建systemd服务文件...")
        
        services = {
            'celery-worker': {
                'description': 'Celery Worker Service',
                'exec_start': f'{sys.executable} -m celery -A app.core.celery_config:celery_app worker --loglevel=info',
                'working_directory': str(self.project_root)
            },
            'celery-beat': {
                'description': 'Celery Beat Service',
                'exec_start': f'{sys.executable} -m celery -A app.core.celery_config:celery_app beat --loglevel=info',
                'working_directory': str(self.project_root)
            },
            'celery-flower': {
                'description': 'Celery Flower Monitoring Service',
                'exec_start': f'{sys.executable} -m celery -A app.core.celery_config:celery_app flower --port=5555',
                'working_directory': str(self.project_root)
            },
            'fastapi-app': {
                'description': 'FastAPI Application Service',
                'exec_start': f'{sys.executable} -m uvicorn app.main:app --host 0.0.0.0 --port 8000',
                'working_directory': str(self.project_root)
            }
        }
        
        systemd_dir = Path('/etc/systemd/system')
        if not systemd_dir.exists():
            logger.warning("systemd目录不存在，跳过服务文件创建")
            return True
        
        for service_name, config in services.items():
            service_file = systemd_dir / f"{service_name}.service"
            
            service_content = f"""[Unit]
Description={config['description']}
After=network.target redis.service mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory={config['working_directory']}
ExecStart={config['exec_start']}
Restart=always
RestartSec=10
Environment=PYTHONPATH={config['working_directory']}

[Install]
WantedBy=multi-user.target
"""
            
            try:
                with open(service_file, 'w') as f:
                    f.write(service_content)
                logger.info(f"✓ 创建服务文件: {service_file}")
            except PermissionError:
                logger.warning(f"权限不足，无法创建服务文件: {service_file}")
                logger.info(f"请手动创建或使用sudo权限")
        
        return True
    
    def create_config_templates(self):
        """创建配置模板"""
        logger.info("创建配置模板...")
        
        self.config_dir.mkdir(exist_ok=True)
        
        # 创建环境变量模板
        env_template = """# Celery工作流系统环境变量配置

# 应用配置
APP_NAME=ai-assistant
APP_VERSION=2.0.0-celery
DEBUG=false

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# MySQL配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=ai_assistant

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/1
CELERY_TASK_SOFT_TIME_LIMIT=1800
CELERY_TASK_TIME_LIMIT=2400
CELERY_WORKER_CONCURRENCY=4

# Worker配置
OCR_WORKER_CONCURRENCY=2
AI_WORKER_CONCURRENCY=4
POST_WORKER_CONCURRENCY=3
API_WORKER_CONCURRENCY=2

# 任务轮询配置
TASK_POLLING_INTERVAL=5
TASK_POLLING_BATCH_SIZE=10

# Flower配置
FLOWER_ENABLED=true
FLOWER_PORT=5555
FLOWER_ADDRESS=0.0.0.0
FLOWER_BASIC_AUTH=admin:password

# 超时配置
OCR_TIMEOUT_SECONDS=300
AGENT_TIMEOUT_SECONDS=180

# 日志配置
LOG_LEVEL=INFO
"""
        
        env_file = self.config_dir / ".env.template"
        with open(env_file, 'w') as f:
            f.write(env_template)
        logger.info(f"✓ 创建环境变量模板: {env_file}")
        
        # 创建Docker Compose配置
        docker_compose = """version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: ai_assistant
      MYSQL_USER: ai_user
      MYSQL_PASSWORD: ai_password
    volumes:
      - mysql_data:/var/lib/mysql

  app:
    build: .
    ports:
      - "8000:8000"
    depends_on:
      - redis
      - mysql
    environment:
      - REDIS_URL=redis://redis:6379/0
      - MYSQL_HOST=mysql
      - MYSQL_USER=ai_user
      - MYSQL_PASSWORD=ai_password
      - MYSQL_DATABASE=ai_assistant
    volumes:
      - ./:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  flower:
    build: .
    ports:
      - "5555:5555"
    depends_on:
      - redis
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
    command: celery -A app.core.celery_config:celery_app flower --port=5555 --address=0.0.0.0

volumes:
  redis_data:
  mysql_data:
"""
        
        docker_file = self.config_dir / "docker-compose.yml"
        with open(docker_file, 'w') as f:
            f.write(docker_compose)
        logger.info(f"✓ 创建Docker Compose配置: {docker_file}")
        
        return True
    
    def create_startup_script(self):
        """创建启动脚本"""
        logger.info("创建启动脚本...")
        
        startup_script = """#!/bin/bash
# Celery工作流系统启动脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "启动Celery工作流系统..."

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "错误: .env文件不存在，请从.env.template复制并配置"
    exit 1
fi

# 启动Redis (如果未运行)
if ! pgrep -x "redis-server" > /dev/null; then
    echo "启动Redis..."
    redis-server --daemonize yes
fi

# 运行数据库迁移
echo "运行数据库迁移..."
python -m app.migrations.migration_runner migrate

# 启动Celery Worker (后台)
echo "启动Celery Worker..."
celery -A app.core.celery_config:celery_app worker --loglevel=info --detach

# 启动Celery Beat (后台)
echo "启动Celery Beat..."
celery -A app.core.celery_config:celery_app beat --loglevel=info --detach

# 启动Flower监控 (后台)
echo "启动Flower监控..."
celery -A app.core.celery_config:celery_app flower --port=5555 --address=0.0.0.0 &

# 启动FastAPI应用
echo "启动FastAPI应用..."
uvicorn app.main:app --host 0.0.0.0 --port 8000

echo "系统启动完成!"
echo "FastAPI应用: http://localhost:8000"
echo "Flower监控: http://localhost:5555"
"""
        
        startup_file = self.scripts_dir / "start_system.sh"
        with open(startup_file, 'w') as f:
            f.write(startup_script)
        
        # 设置执行权限
        os.chmod(startup_file, 0o755)
        logger.info(f"✓ 创建启动脚本: {startup_file}")
        
        return True
    
    def deploy(self, skip_deps=False, skip_services=False, skip_migrations=False):
        """执行完整部署"""
        logger.info("开始部署Celery工作流系统...")
        
        if not skip_deps and not self.check_dependencies():
            logger.error("依赖检查失败，部署终止")
            return False
        
        if not skip_services and not self.check_services():
            logger.error("服务检查失败，部署终止")
            return False
        
        if not skip_migrations and not self.run_migrations():
            logger.error("数据库迁移失败，部署终止")
            return False
        
        if not self.create_config_templates():
            logger.error("配置模板创建失败，部署终止")
            return False
        
        if not self.create_startup_script():
            logger.error("启动脚本创建失败，部署终止")
            return False
        
        self.create_systemd_services()  # 这个可以失败，不影响部署
        
        logger.info("✅ Celery工作流系统部署完成!")
        logger.info("下一步:")
        logger.info("1. 复制 config/.env.template 到项目根目录的 .env 并配置")
        logger.info("2. 运行 scripts/start_system.sh 启动系统")
        logger.info("3. 访问 http://localhost:8000 查看API")
        logger.info("4. 访问 http://localhost:5555 查看Flower监控")
        
        return True


def main():
    parser = argparse.ArgumentParser(description='部署Celery工作流系统')
    parser.add_argument('--skip-deps', action='store_true', help='跳过依赖检查')
    parser.add_argument('--skip-services', action='store_true', help='跳过服务检查')
    parser.add_argument('--skip-migrations', action='store_true', help='跳过数据库迁移')
    parser.add_argument('--project-root', help='项目根目录路径')
    
    args = parser.parse_args()
    
    deployer = CelerySystemDeployer(args.project_root)
    success = deployer.deploy(
        skip_deps=args.skip_deps,
        skip_services=args.skip_services,
        skip_migrations=args.skip_migrations
    )
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()